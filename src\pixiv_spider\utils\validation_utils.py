"""
验证工具类

提供各种验证功能
"""

import re
import os
from typing import List, Optional, Tuple
from urllib.parse import urlparse


class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """
        验证URL是否有效
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def is_valid_pixiv_artwork_url(url: str) -> bool:
        """
        验证是否为有效的Pixiv作品URL
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否为有效的Pixiv作品URL
        """
        pattern = r'https?://(?:www\.)?pixiv\.net/(?:en/)?artworks/\d+'
        return bool(re.match(pattern, url))
    
    @staticmethod
    def extract_artwork_id(url: str) -> Optional[int]:
        """
        从URL中提取作品ID
        
        Args:
            url: Pixiv作品URL
            
        Returns:
            Optional[int]: 作品ID，失败返回None
        """
        try:
            match = re.search(r'/artworks/(\d+)', url)
            if match:
                return int(match.group(1))
        except Exception:
            pass
        return None
    
    @staticmethod
    def is_valid_user_id(user_id: str) -> bool:
        """
        验证用户ID是否有效
        
        Args:
            user_id: 用户ID字符串
            
        Returns:
            bool: 是否有效
        """
        try:
            uid = int(user_id)
            return uid > 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_valid_page_range(start_page: str, end_page: str) -> Tuple[bool, str]:
        """
        验证页码范围是否有效
        
        Args:
            start_page: 起始页码
            end_page: 结束页码
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            start = int(start_page)
            end = int(end_page)
            
            if start < 1:
                return False, "起始页码必须大于0"
            
            if end < start:
                return False, "结束页码必须大于等于起始页码"
            
            if end - start > 100:
                return False, "页码范围不能超过100页"
            
            return True, ""
            
        except (ValueError, TypeError):
            return False, "页码必须是有效的数字"
    
    @staticmethod
    def is_valid_days(days: str) -> Tuple[bool, str]:
        """
        验证天数是否有效
        
        Args:
            days: 天数字符串
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            day_count = int(days)
            
            if day_count < 1:
                return False, "天数必须大于0"
            
            if day_count > 365:
                return False, "天数不能超过365天"
            
            return True, ""
            
        except (ValueError, TypeError):
            return False, "天数必须是有效的数字"
    
    @staticmethod
    def is_valid_directory(path: str) -> Tuple[bool, str]:
        """
        验证目录路径是否有效
        
        Args:
            path: 目录路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not path or not path.strip():
            return False, "路径不能为空"
        
        try:
            # 检查路径是否包含非法字符
            illegal_chars = '<>"|?*'
            for char in illegal_chars:
                if char in path:
                    return False, f"路径包含非法字符: {char}"
            
            # 检查父目录是否存在且可写
            parent_dir = os.path.dirname(os.path.abspath(path))
            if not os.path.exists(parent_dir):
                return False, "父目录不存在"
            
            if not os.access(parent_dir, os.W_OK):
                return False, "没有写入权限"
            
            return True, ""
            
        except Exception as e:
            return False, f"路径验证失败: {e}"
    
    @staticmethod
    def is_valid_keyword(keyword: str) -> Tuple[bool, str]:
        """
        验证搜索关键词是否有效
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not keyword or not keyword.strip():
            return False, "搜索关键词不能为空"
        
        keyword = keyword.strip()
        
        if len(keyword) < 1:
            return False, "搜索关键词太短"
        
        if len(keyword) > 100:
            return False, "搜索关键词太长（最多100个字符）"
        
        # 检查是否包含特殊字符
        illegal_patterns = ['<script', 'javascript:', 'data:']
        keyword_lower = keyword.lower()
        
        for pattern in illegal_patterns:
            if pattern in keyword_lower:
                return False, "搜索关键词包含非法内容"
        
        return True, ""
    
    @staticmethod
    def is_valid_thread_count(count: str) -> Tuple[bool, str]:
        """
        验证线程数是否有效
        
        Args:
            count: 线程数字符串
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            thread_count = int(count)
            
            if thread_count < 1:
                return False, "线程数必须大于0"
            
            if thread_count > 32:
                return False, "线程数不能超过32"
            
            return True, ""
            
        except (ValueError, TypeError):
            return False, "线程数必须是有效的数字"
    
    @staticmethod
    def validate_all_settings(settings: dict) -> List[str]:
        """
        验证所有设置
        
        Args:
            settings: 设置字典
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        # 验证页码范围
        if 'start_page' in settings and 'end_page' in settings:
            valid, error = ValidationUtils.is_valid_page_range(
                settings['start_page'], settings['end_page']
            )
            if not valid:
                errors.append(f"页码范围: {error}")
        
        # 验证天数
        if 'days' in settings:
            valid, error = ValidationUtils.is_valid_days(settings['days'])
            if not valid:
                errors.append(f"天数: {error}")
        
        # 验证保存路径
        if 'save_path' in settings:
            valid, error = ValidationUtils.is_valid_directory(settings['save_path'])
            if not valid:
                errors.append(f"保存路径: {error}")
        
        # 验证搜索关键词（如果启用搜索模式）
        if settings.get('download_mode') == 'search' and 'search_keyword' in settings:
            valid, error = ValidationUtils.is_valid_keyword(settings['search_keyword'])
            if not valid:
                errors.append(f"搜索关键词: {error}")
        
        # 验证用户ID（如果启用用户模式）
        if settings.get('download_mode') == 'user' and 'user_id' in settings:
            if not ValidationUtils.is_valid_user_id(settings['user_id']):
                errors.append("用户ID: 必须是有效的正整数")
        
        # 验证线程数
        if 'max_workers' in settings:
            valid, error = ValidationUtils.is_valid_thread_count(settings['max_workers'])
            if not valid:
                errors.append(f"工作线程数: {error}")
        
        if 'concurrent_downloads' in settings:
            valid, error = ValidationUtils.is_valid_thread_count(settings['concurrent_downloads'])
            if not valid:
                errors.append(f"并发下载数: {error}")
        
        return errors
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        清理文件名
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        # 替换非法字符
        illegal_chars = '<>:"/\\|?*'
        clean_name = filename
        
        for char in illegal_chars:
            clean_name = clean_name.replace(char, '_')
        
        # 移除控制字符
        clean_name = ''.join(char for char in clean_name if ord(char) >= 32)
        
        # 去除首尾空格和点
        clean_name = clean_name.strip(' .')
        
        # 如果为空，返回默认名称
        if not clean_name:
            clean_name = "unnamed"
        
        return clean_name 