"""
Pixiv Spider 现代化登录对话框 (CustomTkinter版本)

使用CustomTkinter重构的现代化登录界面
"""

import customtkinter as ctk
import tkinter as tk
import threading
import webbrowser
from typing import Optional, Dict, Any, Tuple


class ModernLoginDialog:
    """现代化登录对话框"""
    
    def __init__(self, parent, auth_service):
        """
        初始化现代化登录对话框

        Args:
            parent: 父窗口
            auth_service: 认证服务
        """
        self.parent = parent
        self.auth_service = auth_service
        self.result = None
        self.cookies = None
        self.selenium_driver = None  # Selenium驱动实例

        # 创建对话框窗口
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("Pixiv 登录 - 现代化界面")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_window()

        # 创建界面
        self._create_widgets()

        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)

    def _center_window(self):
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        
        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # 计算对话框位置
        dialog_width = 600
        dialog_height = 500
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🔐 Pixiv 登录", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 副标题
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="现代化登录界面",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 说明文本框
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="both", expand=True, padx=10, pady=(0, 20))
        
        info_textbox = ctk.CTkTextbox(
            info_frame,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        info_textbox.pack(fill="both", expand=True, padx=15, pady=15)
        
        # 添加说明内容
        info_content = """🔐 Pixiv 登录说明

请按照以下步骤完成登录：

1. 点击下方的"🌐 打开登录页面"按钮
2. 在打开的浏览器中登录你的Pixiv账号
3. 登录成功后，确保能正常访问Pixiv主页
4. 回到这个对话框，点击"✅ 确认登录完成"按钮

💡 提示：
• 如果浏览器没有自动打开，请手动访问：https://www.pixiv.net/
• 登录时可能需要验证码或邮箱确认
• 请保持浏览器打开状态直到登录完成
• 支持暗色/亮色主题切换，提供更好的视觉体验

⚠️ 注意：
• 请确保网络连接正常
• 建议使用Chrome浏览器以获得最佳体验
• 登录过程中请勿关闭浏览器窗口"""
        
        info_textbox.insert("1.0", info_content)
        info_textbox.configure(state="disabled")
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # 配置按钮框架的网格
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)
        button_frame.grid_columnconfigure(2, weight=1)
        
        # 打开登录页面按钮
        self.open_btn = ctk.CTkButton(
            button_frame,
            text="🌐 打开登录页面",
            command=self._open_login_page,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.open_btn.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        # 确认登录完成按钮
        self.confirm_btn = ctk.CTkButton(
            button_frame,
            text="✅ 确认登录完成",
            command=self._confirm_login,
            state="disabled",
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.confirm_btn.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ 取消",
            command=self._on_cancel,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="gray",
            hover_color="darkgray"
        )
        cancel_btn.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            main_frame, 
            text="", 
            font=ctk.CTkFont(size=12),
            text_color="blue"
        )
        self.status_label.pack(pady=(10, 0))
    
    def _open_login_page(self):
        """打开登录页面（使用Selenium控制的浏览器）"""
        try:
            self.status_label.configure(text="正在启动浏览器...", text_color="blue")
            self.open_btn.configure(state="disabled")
            self.dialog.update()

            # 在后台线程中启动Selenium浏览器
            def start_browser():
                try:
                    from selenium import webdriver
                    from selenium.webdriver.chrome.options import Options

                    # 设置Chrome选项
                    options = Options()
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                    options.add_argument('--disable-gpu')
                    options.add_argument('--window-size=1200,800')

                    # 启动浏览器
                    self.selenium_driver = webdriver.Chrome(options=options)
                    self.selenium_driver.get('https://www.pixiv.net/')

                    # 在主线程中更新UI
                    self.dialog.after(0, self._browser_opened_successfully)

                except Exception as e:
                    # 在主线程中显示错误
                    self.dialog.after(0, lambda: self._browser_open_failed(e))

            threading.Thread(target=start_browser, daemon=True).start()

        except Exception as e:
            self._browser_open_failed(e)
    
    def _browser_opened_successfully(self):
        """浏览器启动成功"""
        self.status_label.configure(text="✅ 浏览器已启动，请在浏览器中完成登录", text_color="green")
        self.confirm_btn.configure(state="normal")
        self.open_btn.configure(state="normal", text="🔄 重新打开页面")
    
    def _browser_open_failed(self, error):
        """浏览器启动失败"""
        self.status_label.configure(text=f"❌ 启动浏览器失败: {error}", text_color="red")
        self.open_btn.configure(state="normal")
        
        # 尝试使用系统默认浏览器
        try:
            webbrowser.open('https://www.pixiv.net/')
            self.status_label.configure(text="🌐 已使用系统默认浏览器打开登录页面", text_color="orange")
            self.confirm_btn.configure(state="normal")
        except Exception:
            self.status_label.configure(text="❌ 无法打开浏览器，请手动访问 https://www.pixiv.net/", text_color="red")
    
    def _confirm_login(self):
        """确认登录完成"""
        self.status_label.configure(text="正在验证登录状态...", text_color="blue")
        self.confirm_btn.configure(state="disabled")
        
        def verify_login():
            try:
                # 从Selenium浏览器获取cookies
                if self.selenium_driver:
                    cookies = {}
                    for cookie in self.selenium_driver.get_cookies():
                        cookies[cookie['name']] = cookie['value']
                    
                    # 验证登录状态
                    success = self.auth_service.verify_cookies(cookies)
                    self.dialog.after(0, lambda: self._handle_login_result(success, cookies))
                else:
                    # 如果没有Selenium驱动，尝试其他验证方式
                    success = self.auth_service.verify_login()
                    self.dialog.after(0, lambda: self._handle_login_result(success, None))
                    
            except Exception as e:
                self.dialog.after(0, lambda: self._handle_login_error(e))
        
        threading.Thread(target=verify_login, daemon=True).start()
    
    def _handle_login_result(self, success: bool, cookies: Optional[Dict[str, Any]]):
        """处理登录结果"""
        if success and cookies:
            self.status_label.configure(text=f"✅ 登录成功！获得 {len(cookies)} 个cookie", text_color="green")
            self.result = True
            self.cookies = cookies
            
            # 延迟关闭对话框
            self.dialog.after(1500, self._close_dialog)
        else:
            self.status_label.configure(text="❌ 检测到未登录状态，请重新登录", text_color="red")
            self.confirm_btn.configure(state="normal")
    
    def _handle_login_error(self, error: Exception):
        """处理登录错误"""
        self.status_label.configure(text=f"❌ 登录验证失败: {error}", text_color="red")
        self.confirm_btn.configure(state="normal")
    
    def _on_cancel(self):
        """取消登录"""
        self.result = False
        self._close_dialog()
    
    def _close_dialog(self):
        """关闭对话框"""
        # 清理Selenium驱动
        if self.selenium_driver:
            try:
                self.selenium_driver.quit()
            except Exception:
                pass
            self.selenium_driver = None

        self.dialog.grab_release()
        self.dialog.destroy()
    
    def show(self) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        显示对话框并等待结果
        
        Returns:
            Tuple[bool, Optional[Dict]]: (是否成功, Cookie数据)
        """
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result or False, self.cookies
