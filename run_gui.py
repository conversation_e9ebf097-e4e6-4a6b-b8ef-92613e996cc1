#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pixiv Spider GUI 启动器

启动重构后的GUI界面
"""

import sys
import os
import logging
from pathlib import Path

# 添加源代码路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("=" * 60)
    print("🎨 Pixiv Spider - 重构版本 GUI")
    print("=" * 60)
    print()
    
    try:
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        
        print("🔄 正在导入模块...")
        
        try:
            from pixiv_spider.gui import create_gui
            from pixiv_spider.config import ConfigManager
        except ImportError as e:
            print(f"❌ 导入错误: {e}")
            print("\n详细错误信息:")
            import traceback
            traceback.print_exc()
            print("\n📋 可能的解决方案:")
            print("1. 检查是否安装了所有必需的依赖包")
            print("2. 运行: pip install -r requirements.txt")
            print("3. 确保 tkinter 已安装")
            input("\n按回车键退出...")
            return
        
        print("✅ 模块导入成功")
        
        # 创建配置管理器
        print("🔧 正在初始化配置...")
        config_manager = ConfigManager()
        
        # 创建GUI应用
        print("🚀 正在启动GUI界面...")
        app = create_gui(config_manager)
        
        print("🎉 GUI启动成功！")
        print("💡 提示: 首次使用请点击'重新登录'按钮进行登录")
        print()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n详细错误信息:")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main() 