"""
搜索管理器

负责管理搜索相关的业务逻辑
"""

import logging
from typing import List, Set, Optional
from ..models.artwork import Artwork
from ..utils.selenium_utils import SeleniumDriver


class SearchManager:
    """搜索管理器"""
    
    def __init__(self, selenium_driver: Optional[SeleniumDriver] = None):
        """
        初始化搜索管理器
        
        Args:
            selenium_driver: Selenium驱动器
        """
        self.selenium_driver = selenium_driver
        self.logger = logging.getLogger(__name__)
    
    def search_artworks_by_keyword(self, keyword: str, pages: int = 1) -> Set[str]:
        """
        根据关键词搜索作品
        
        Args:
            keyword: 搜索关键词
            pages: 搜索页数
            
        Returns:
            Set[str]: 作品链接集合
        """
        if not self.selenium_driver:
            self.logger.error("Selenium驱动器未初始化")
            return set()
        
        self.logger.info(f"开始搜索关键词: {keyword}, 页数: {pages}")
        
        # 生成搜索页面URL列表
        page_urls = []
        for page in range(1, pages + 1):
            url = f"https://www.pixiv.net/tags/{keyword}/artworks?p={page}"
            page_urls.append(url)
        
        # 批量获取页面链接
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls)
        
        self.logger.info(f"搜索完成，找到 {len(artwork_links)} 个作品链接")
        return artwork_links
    
    def search_user_artworks(self, user_id: str, pages: int = 1) -> Set[str]:
        """
        搜索用户作品
        
        Args:
            user_id: 用户ID
            pages: 搜索页数
            
        Returns:
            Set[str]: 作品链接集合
        """
        if not self.selenium_driver:
            self.logger.error("Selenium驱动器未初始化")
            return set()
        
        self.logger.info(f"开始搜索用户作品: {user_id}, 页数: {pages}")
        
        # 获取用户作品链接
        artwork_links = self.selenium_driver.get_user_artworks(user_id, pages)
        
        self.logger.info(f"用户作品搜索完成，找到 {len(artwork_links)} 个作品链接")
        return artwork_links
    
    def search_ranking_artworks(self, mode: str = "daily", pages: int = 1) -> Set[str]:
        """
        搜索排行榜作品
        
        Args:
            mode: 排行榜模式
            pages: 搜索页数
            
        Returns:
            Set[str]: 作品链接集合
        """
        if not self.selenium_driver:
            self.logger.error("Selenium驱动器未初始化")
            return set()
        
        self.logger.info(f"开始搜索排行榜作品: {mode}, 页数: {pages}")
        
        # 生成排行榜页面URL列表
        page_urls = []
        for page in range(1, pages + 1):
            url = f"https://www.pixiv.net/ranking.php?mode={mode}&p={page}"
            page_urls.append(url)
        
        # 批量获取页面链接
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls)
        
        self.logger.info(f"排行榜搜索完成，找到 {len(artwork_links)} 个作品链接")
        return artwork_links
