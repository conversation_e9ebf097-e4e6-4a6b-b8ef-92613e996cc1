"""
Selenium工具类

封装Selenium WebDriver的常用操作
"""

import os
import re
import time
import logging
from typing import List, Set, Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from ..models.exceptions import SeleniumError
from ..config.config_manager import ConfigManager


class SeleniumDriver:
    """Selenium驱动器工具类"""

    # 统一的选择器配置
    SELECTORS = {
        'artwork_links': [
            'a[href*="/artworks/"]',
            'a[href^="/artworks/"]',
            '.gtm-new-work-thumbnail-click',
            '[data-gtm-value*="artworks"]'
        ],
        'user_menu': [
            'a[data-gtm-value="header_user_menu"]',
            'img[alt*="avatar"]',
            '.user-icon',
            '[data-testid="header-user-menu"]',
            '.header-menu'
        ],
        'login_buttons': [
            'a[href*="login"]',
            'button[data-gtm-value="header_login"]',
            '.signup-form'
        ]
    }

    def __init__(self, cookies: List[Dict[str, Any]], config_manager: Optional[ConfigManager] = None):
        """
        初始化Selenium驱动器
        
        Args:
            cookies: 认证cookies
            config_manager: 配置管理器
        """
        self.cookies = cookies
        self.config_manager = config_manager or ConfigManager()
        self.spider_config = self.config_manager.load_spider_config()
        self.logger = logging.getLogger(__name__)

        self.driver: Optional[webdriver.Chrome] = None

        # 页面链接缓存
        self._page_cache = {}
        self._cache_max_size = 100  # 最大缓存页面数

        self._setup_driver()
    
    def _setup_driver(self) -> None:
        """设置驱动器"""
        try:
            options = Options()
            
            # 基本选项
            if self.spider_config.selenium_headless:
                options.add_argument('--headless')

            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')

            # 抑制日志和错误信息
            options.add_argument('--disable-logging')
            options.add_argument('--log-level=3')  # 只显示致命错误
            options.add_argument('--silent')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-translate')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-default-apps')
            
            # 实验性选项
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_experimental_option("detach", True)

            # 抑制Chrome日志
            options.add_experimental_option('excludeSwitches', ['enable-logging'])
            options.add_experimental_option('useAutomationExtension', False)

            # 设置日志首选项
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2  # 禁用通知
                }
            }
            options.add_experimental_option("prefs", prefs)
            
            # 用户代理
            options.add_argument(f'--user-agent={self.spider_config.user_agent}')
            
            # 代理设置
            if self.spider_config.proxy:
                options.add_argument(f'--proxy-server={self.spider_config.proxy}')

            # 创建Chrome服务，抑制日志
            service = Service()
            service.log_path = os.devnull if hasattr(os, 'devnull') else 'NUL'  # Windows使用NUL，Unix使用/dev/null

            self.driver = webdriver.Chrome(service=service, options=options)
            
            # 设置超时
            self.driver.set_page_load_timeout(self.spider_config.selenium_page_load_timeout)
            self.driver.implicitly_wait(self.spider_config.selenium_timeout)
            
            # 执行脚本隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置cookies
            self._set_cookies()
            
            self.logger.info("Selenium驱动器初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化Selenium驱动器失败: {e}")
            raise SeleniumError(f"初始化驱动器失败: {e}")
    
    def _set_cookies(self) -> None:
        """设置cookies - 优化版本"""
        if not self.cookies:
            self.logger.warning("没有cookies可设置")
            return
            
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试设置cookies (第{attempt + 1}次)")
                
                # 设置较短的超时时间
                original_timeout = self.spider_config.selenium_page_load_timeout
                self.driver.set_page_load_timeout(15)
                
                try:
                    # 访问pixiv主页
                    self.driver.get('https://www.pixiv.net/')
                    time.sleep(1)  # 减少等待时间
                    
                    # 删除现有cookies
                    self.driver.delete_all_cookies()
                    
                    # 添加cookies
                    success_count = 0
                    for cookie in self.cookies:
                        try:
                            # 清理不必要的字段
                            clean_cookie = {
                                'name': cookie['name'],
                                'value': cookie['value'],
                                'domain': cookie.get('domain', '.pixiv.net'),
                                'path': cookie.get('path', '/'),
                            }
                            
                            # 处理安全属性
                            if 'secure' in cookie and cookie['secure']:
                                clean_cookie['secure'] = True
                            if 'httpOnly' in cookie and cookie['httpOnly']:
                                clean_cookie['httpOnly'] = True
                            
                            self.driver.add_cookie(clean_cookie)
                            success_count += 1
                            
                        except Exception as e:
                            self.logger.debug(f"跳过cookie {cookie.get('name', 'unknown')}: {e}")
                            continue
                    
                    # 轻量刷新
                    self.driver.refresh()
                    time.sleep(1)
                    
                    self.logger.info(f"成功设置 {success_count}/{len(self.cookies)} 个cookie")
                    return  # 成功设置，退出重试循环
                    
                finally:
                    # 恢复原始超时
                    self.driver.set_page_load_timeout(original_timeout)
                    
            except Exception as e:
                self.logger.warning(f"第{attempt + 1}次设置cookies失败: {e}")
                if attempt == max_retries - 1:
                    self.logger.error("所有重试均失败，继续运行但可能影响登录状态")
                    return  # 不抛出异常，允许程序继续运行
                time.sleep(2)  # 重试前等待
    
    def get_page_links_batch(self, page_urls: List[str], max_concurrent: int = 3) -> Set[str]:
        """
        批量获取页面链接（优化版本）

        Args:
            page_urls: 页面URL列表
            max_concurrent: 最大并发数（考虑到Selenium的限制，不宜过高）

        Returns:
            Set[str]: 作品链接集合
        """
        if not page_urls:
            return set()

        all_links = set()

        # 分批处理，避免过多并发
        batch_size = max_concurrent
        total_batches = (len(page_urls) + batch_size - 1) // batch_size

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(page_urls))
            batch_urls = page_urls[start_idx:end_idx]

            self.logger.info(f"处理批次 {batch_idx + 1}/{total_batches}: {len(batch_urls)} 个页面")

            # 处理当前批次
            batch_links = self._process_url_batch(batch_urls)
            all_links.update(batch_links)

            # 批次间延迟
            if batch_idx < total_batches - 1:
                time.sleep(2)

        self.logger.info(f"批量获取完成，共找到 {len(all_links)} 个作品链接")
        return all_links

    def _process_url_batch(self, urls: List[str]) -> Set[str]:
        """
        处理一批URL（串行处理，但优化了单页处理逻辑）

        Args:
            urls: URL列表

        Returns:
            Set[str]: 作品链接集合
        """
        batch_links = set()

        for i, url in enumerate(urls):
            try:
                self.logger.debug(f"处理页面 {i+1}/{len(urls)}: {url}")
                links = self.get_single_page_links_optimized(url)
                batch_links.update(links)

                # 页面间短暂延迟
                if i < len(urls) - 1:
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"获取页面链接失败: {url}, 错误: {e}")
                continue

        return batch_links

    def _wait_for_page_ready(self, timeout: int = 10) -> bool:
        """
        智能等待页面准备就绪

        Args:
            timeout: 超时时间

        Returns:
            bool: 页面是否准备就绪
        """
        try:
            # 等待页面基本加载完成
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            # 等待作品容器出现
            selectors_to_wait = [
                'section[role="main"]',  # 主要内容区域
                '[data-gtm-value*="artworks"]',  # 作品相关元素
                'a[href*="/artworks/"]',  # 作品链接
                '.sc-l7cibp-0'  # 可能的作品容器类名
            ]

            for selector in selectors_to_wait:
                try:
                    WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    return True
                except TimeoutException:
                    continue

            # 如果没有找到预期元素，等待一下再继续
            time.sleep(2)
            return True

        except Exception as e:
            self.logger.warning(f"等待页面就绪失败: {e}")
            return False
    
    def get_single_page_links(self, url: str) -> Set[str]:
        """
        获取单个页面的作品链接（保持向后兼容）

        Args:
            url: 页面URL

        Returns:
            Set[str]: 作品链接集合
        """
        return self.get_single_page_links_optimized(url)

    def get_single_page_links_optimized(self, url: str) -> Set[str]:
        """
        获取单个页面的作品链接（优化版本，带缓存）

        Args:
            url: 页面URL

        Returns:
            Set[str]: 作品链接集合
        """
        # 检查缓存
        if url in self._page_cache:
            self.logger.debug(f"使用缓存的页面链接: {url}")
            return self._page_cache[url].copy()

        try:
            # 加载页面
            self.driver.get(url)

            # 智能等待页面加载
            if not self._wait_for_page_ready():
                self.logger.warning(f"页面加载超时: {url}")
                return set()

            # 智能滚动加载更多内容
            self._smart_scroll_optimized()

            # 获取所有作品链接
            artwork_links = self._extract_artwork_links_optimized()

            # 缓存结果
            self._cache_page_links(url, artwork_links)

            self.logger.debug(f"从页面 {url} 获取到 {len(artwork_links)} 个作品链接")
            return artwork_links

        except Exception as e:
            self.logger.error(f"获取页面链接失败: {url}, 错误: {e}")
            return set()

    def _cache_page_links(self, url: str, links: Set[str]) -> None:
        """
        缓存页面链接

        Args:
            url: 页面URL
            links: 作品链接集合
        """
        # 如果缓存已满，移除最旧的条目
        if len(self._page_cache) >= self._cache_max_size:
            # 移除第一个（最旧的）条目
            oldest_url = next(iter(self._page_cache))
            del self._page_cache[oldest_url]

        self._page_cache[url] = links.copy()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计
        """
        return {
            'cache_size': len(self._page_cache),
            'cache_max_size': self._cache_max_size,
            'cache_hit_rate': self._calculate_cache_hit_rate(),
            'driver_status': 'active' if self.driver else 'inactive'
        }

    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率（简化版本）"""
        # 这里可以添加更复杂的统计逻辑
        return len(self._page_cache) / max(self._cache_max_size, 1) * 100

    def clear_cache(self) -> None:
        """清理页面缓存"""
        cache_size = len(self._page_cache)
        self._page_cache.clear()
        self.logger.info(f"页面缓存已清理，释放了 {cache_size} 个缓存项")
    
    def _smart_scroll(self) -> None:
        """智能滚动策略（保持向后兼容）"""
        self._smart_scroll_optimized()

    def _smart_scroll_optimized(self) -> None:
        """优化的智能滚动策略"""
        try:
            # 首先检查是否需要滚动
            initial_links = len(self._extract_artwork_links_optimized())

            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 8  # 减少最大滚动次数
            no_new_content_count = 0

            while scroll_attempts < max_scrolls and no_new_content_count < 2:
                # 渐进式滚动，而不是直接滚动到底部
                current_position = self.driver.execute_script("return window.pageYOffset")
                scroll_step = 800  # 每次滚动800像素
                new_position = current_position + scroll_step

                self.driver.execute_script(f"window.scrollTo(0, {new_position});")

                # 动态等待时间，根据网络状况调整
                time.sleep(1.5)

                # 检查是否有新内容加载
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                current_links = len(self._extract_artwork_links_optimized())

                # 如果高度没有变化且链接数量没有增加
                if new_height == last_height and current_links <= initial_links:
                    no_new_content_count += 1
                else:
                    no_new_content_count = 0
                    initial_links = current_links

                last_height = new_height
                scroll_attempts += 1

                # 如果已经滚动到底部，提前退出
                if new_position >= new_height:
                    break

            # 滚动回顶部，但不等待太久
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(0.5)

        except Exception as e:
            self.logger.warning(f"智能滚动失败: {e}")
    
    def wait_for_element(self, selector: str, timeout: int = None) -> bool:
        """
        等待元素出现
        
        Args:
            selector: CSS选择器
            timeout: 超时时间
            
        Returns:
            bool: 是否找到元素
        """
        if timeout is None:
            timeout = self.spider_config.selenium_timeout
        
        try:
            wait = WebDriverWait(self.driver, timeout)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
            return True
        except TimeoutException:
            return False
        except Exception as e:
            self.logger.error(f"等待元素失败: {e}")
            return False
    
    def _find_elements_by_selectors(self, selector_group: str) -> bool:
        """
        使用选择器组查找元素

        Args:
            selector_group: 选择器组名称

        Returns:
            bool: 是否找到元素
        """
        selectors = self.SELECTORS.get(selector_group, [])
        for selector in selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    return True
            except Exception:
                continue
        return False

    def _extract_artwork_links(self) -> Set[str]:
        """
        从当前页面提取作品链接（保持向后兼容）

        Returns:
            Set[str]: 作品链接集合
        """
        return self._extract_artwork_links_optimized()

    def _extract_artwork_links_optimized(self) -> Set[str]:
        """
        优化的作品链接提取

        Returns:
            Set[str]: 作品链接集合
        """
        artwork_links = set()

        # 按优先级排序的选择器
        prioritized_selectors = [
            'a[href*="/artworks/"]',  # 最常见的作品链接
            'a[href^="/artworks/"]',  # 相对路径的作品链接
            '.gtm-new-work-thumbnail-click',  # 特定的作品缩略图
            '[data-gtm-value*="artworks"]'  # 包含artworks的数据属性
        ]

        # 尝试使用单个CSS选择器一次性获取所有链接
        try:
            # 组合选择器，一次查询
            combined_selector = ', '.join(prioritized_selectors)
            elements = self.driver.find_elements(By.CSS_SELECTOR, combined_selector)

            for element in elements:
                href = element.get_attribute('href')
                if href and '/artworks/' in href:
                    # 标准化URL
                    if not href.startswith('https://'):
                        href = 'https://www.pixiv.net' + href
                    artwork_links.add(href)

        except Exception as e:
            # 如果组合选择器失败，回退到逐个尝试
            self.logger.debug(f"组合选择器失败，回退到逐个尝试: {e}")
            for selector in prioritized_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and '/artworks/' in href:
                            # 标准化URL
                            if not href.startswith('https://'):
                                href = 'https://www.pixiv.net' + href
                            artwork_links.add(href)
                except Exception:
                    continue

        # 验证和清理链接
        validated_links = self._validate_artwork_links(artwork_links)
        return validated_links

    def _validate_artwork_links(self, links: Set[str]) -> Set[str]:
        """
        验证和清理作品链接

        Args:
            links: 原始链接集合

        Returns:
            Set[str]: 验证后的链接集合
        """
        validated_links = set()

        for link in links:
            try:
                # 提取作品ID进行验证
                import re
                match = re.search(r'/artworks/(\d+)', link)
                if match:
                    artwork_id = match.group(1)
                    # 确保ID是有效的数字
                    if artwork_id.isdigit() and len(artwork_id) <= 12:  # Pixiv作品ID通常不超过12位
                        # 标准化URL格式
                        standard_url = f"https://www.pixiv.net/artworks/{artwork_id}"
                        validated_links.add(standard_url)
            except Exception:
                continue

        if len(validated_links) != len(links):
            self.logger.debug(f"链接验证: {len(links)} -> {len(validated_links)}")

        return validated_links

    def check_login_status(self) -> bool:
        """
        检查登录状态

        Returns:
            bool: 是否已登录
        """
        try:
            self.driver.get('https://www.pixiv.net/')
            time.sleep(3)

            # 检查登录相关元素
            if self._find_elements_by_selectors('user_menu'):
                self.logger.info("检测到登录状态")
                return True

            # 检查是否有登录按钮（说明未登录）
            if self._find_elements_by_selectors('login_buttons'):
                self.logger.warning("检测到登录按钮，可能未登录")
                return False
            
            # 检查URL
            current_url = self.driver.current_url
            if 'login' in current_url or 'signup' in current_url:
                self.logger.warning("当前在登录页面")
                return False
            
            self.logger.info("无法确定登录状态，假设已登录")
            return True
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def get_search_results(self, keyword: str, pages: int = 5) -> Set[str]:
        """
        搜索作品并获取结果链接
        
        Args:
            keyword: 搜索关键词
            pages: 搜索页数
            
        Returns:
            Set[str]: 作品链接集合
        """
        all_links = set()
        
        try:
            for page in range(1, pages + 1):
                search_url = f"https://www.pixiv.net/tags/{keyword}/artworks?p={page}"
                links = self.get_single_page_links(search_url)
                all_links.update(links)
                
                # 添加延迟
                time.sleep(2)
            
            self.logger.info(f"搜索 '{keyword}' 完成，共找到 {len(all_links)} 个作品")
            return all_links
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            return set()
    
    def get_user_artworks(self, user_id: int, pages: int = 5) -> Set[str]:
        """
        获取用户作品链接
        
        Args:
            user_id: 用户ID
            pages: 页数
            
        Returns:
            Set[str]: 作品链接集合
        """
        all_links = set()
        
        try:
            for page in range(1, pages + 1):
                user_url = f"https://www.pixiv.net/users/{user_id}/artworks?p={page}"
                links = self.get_single_page_links(user_url)
                all_links.update(links)
                
                # 如果没有找到新链接，说明没有更多页面
                if not links:
                    break
                
                time.sleep(2)
            
            self.logger.info(f"用户 {user_id} 作品获取完成，共 {len(all_links)} 个作品")
            return all_links
            
        except Exception as e:
            self.logger.error(f"获取用户作品失败: {e}")
            return set()
    
    def quit(self) -> None:
        """关闭驱动器"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("Selenium驱动器已关闭")
            except Exception as e:
                self.logger.error(f"关闭驱动器失败: {e}")
            finally:
                self.driver = None
    
    def __enter__(self):
        """支持上下文管理器"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持上下文管理器"""
        self.quit()
    
    def __del__(self):
        """析构函数"""
        self.quit() 