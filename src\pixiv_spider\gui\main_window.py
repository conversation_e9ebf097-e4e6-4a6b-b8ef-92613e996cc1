"""
Pixiv Spider 主窗口GUI

重构后的图形用户界面，使用新的架构
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import time
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from ..core.pixiv_spider import PixivSpider
from ..config.config_manager import ConfigManager
from ..models.config import (
    DownloadConfig, SpiderConfig, DownloadMode, ClassifyMode, GifMode,
    RankingConfig, RankingCategory, RankingRating, RankingPeriod
)
from ..models.exceptions import PixivSpiderError, AuthenticationError


class PixivSpiderGUI:
    """Pixiv Spider 图形用户界面"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化GUI应用
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.spider = PixivSpider(self.config_manager)
        
        # 加载配置
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 状态变量
        self.is_running = False
        self.is_authenticated = False
        self.download_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'total': 0,
            'completed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 排行榜配置变量
        self.ranking_category_var = None
        self.ranking_rating_var = None
        self.ranking_period_var = None
        self.ranking_date_var = None
        self.ranking_custom_path_var = None
        self.ranking_auto_naming_var = None
        
        # 创建GUI
        self.root = tk.Tk()
        self.setup_ui()
        self.setup_callbacks()
        self.load_settings()
        
        # 初始化检查登录状态
        self.check_login_status()
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("Pixiv Spider - 重构版本 v3.0.0")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(8, weight=1)  # 日志区域可扩展
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="Pixiv Spider - 重构版本", 
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 设置各个UI部分
        self._setup_login_frame(main_frame)
        self._setup_download_settings_frame(main_frame)
        self._setup_performance_frame(main_frame)
        self._setup_control_frame(main_frame)
        self._setup_progress_frame(main_frame)
        self._setup_stats_frame(main_frame)
        self._setup_log_frame(main_frame)
    
    def _setup_login_frame(self, parent):
        """设置登录状态框架"""
        login_frame = ttk.LabelFrame(parent, text="登录状态", padding="10")
        login_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        login_frame.columnconfigure(2, weight=1)
        
        self.login_status_label = ttk.Label(login_frame, text="检查中...", foreground="orange")
        self.login_status_label.grid(row=0, column=0, sticky=tk.W)
        
        ttk.Button(
            login_frame, 
            text="检查登录", 
            command=self.check_login_status
        ).grid(row=0, column=1, padx=(10, 5))
        
        ttk.Button(
            login_frame, 
            text="重新登录", 
            command=self.start_login
        ).grid(row=0, column=2, padx=(5, 0))
    
    def _setup_download_settings_frame(self, parent):
        """设置下载设置框架"""
        settings_frame = ttk.LabelFrame(parent, text="下载设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # 下载模式
        ttk.Label(settings_frame, text="下载模式:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.download_mode_var = tk.StringVar(value=self.download_config.download_mode.value)
        mode_frame = ttk.Frame(settings_frame)
        mode_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Radiobutton(
            mode_frame, text="关注画师新作", variable=self.download_mode_var, value="date",
            command=self._on_download_mode_changed
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            mode_frame, text="排行榜", variable=self.download_mode_var, value="ranking",
            command=self._on_download_mode_changed
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            mode_frame, text="搜索", variable=self.download_mode_var, value="search",
            command=self._on_download_mode_changed
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            mode_frame, text="用户", variable=self.download_mode_var, value="user",
            command=self._on_download_mode_changed
        ).pack(side=tk.LEFT)
        
        row += 1
        
        # 搜索关键词
        self.search_keyword_label = ttk.Label(settings_frame, text="搜索关键词:")
        self.search_keyword_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.search_keyword_var = tk.StringVar(value=self.download_config.search_keyword)
        self.search_keyword_entry = ttk.Entry(settings_frame, textvariable=self.search_keyword_var)
        self.search_keyword_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))

        row += 1

        # 搜索种类
        self.search_category_label = ttk.Label(settings_frame, text="搜索种类:")
        self.search_category_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.search_category_frame = ttk.Frame(settings_frame)
        self.search_category_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)

        from pixiv_spider.models.config import SearchCategory
        self.search_category_var = tk.StringVar(value=self.download_config.search_config.category.value)
        category_combo = ttk.Combobox(
            self.search_category_frame,
            textvariable=self.search_category_var,
            values=[cat.value for cat in SearchCategory],
            state="readonly",
            width=10
        )
        category_combo.pack(side=tk.LEFT)

        # 收藏值过滤
        ttk.Label(self.search_category_frame, text=" 收藏数:").pack(side=tk.LEFT, padx=(10, 0))
        from pixiv_spider.models.config import SearchBookmarkCount
        self.search_bookmark_var = tk.StringVar(value=str(self.download_config.search_config.bookmark_count.value))
        bookmark_values = ["不启用"] + [f"{count.value}users入り" for count in SearchBookmarkCount if count != SearchBookmarkCount.DISABLED]
        bookmark_combo = ttk.Combobox(
            self.search_category_frame,
            textvariable=self.search_bookmark_var,
            values=bookmark_values,
            state="readonly",
            width=12
        )
        bookmark_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 内容模式
        ttk.Label(self.search_category_frame, text=" 内容:").pack(side=tk.LEFT, padx=(10, 0))
        from pixiv_spider.models.config import SearchContentMode
        self.search_content_mode_var = tk.StringVar(value=self.download_config.search_config.content_mode.value)
        content_mode_combo = ttk.Combobox(
            self.search_category_frame,
            textvariable=self.search_content_mode_var,
            values=[mode.value for mode in SearchContentMode],
            state="readonly",
            width=8
        )
        content_mode_combo.pack(side=tk.LEFT, padx=(5, 0))





        row += 1
        
        # 用户ID
        self.user_id_label = ttk.Label(settings_frame, text="用户ID:")
        self.user_id_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.user_id_var = tk.StringVar(value=str(self.download_config.user_id) if self.download_config.user_id else "")
        self.user_id_entry = ttk.Entry(settings_frame, textvariable=self.user_id_var)
        self.user_id_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        
        row += 1
        
        # 页码范围
        self.page_range_label = ttk.Label(settings_frame, text="页码范围:")
        self.page_range_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.page_frame = ttk.Frame(settings_frame)
        self.page_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)

        self.start_page_var = tk.StringVar(value=str(self.download_config.start_page))
        self.end_page_var = tk.StringVar(value=str(self.download_config.end_page))

        self.start_page_entry = ttk.Entry(self.page_frame, textvariable=self.start_page_var, width=8)
        self.start_page_entry.pack(side=tk.LEFT)
        ttk.Label(self.page_frame, text=" 到 ").pack(side=tk.LEFT)
        self.end_page_entry = ttk.Entry(self.page_frame, textvariable=self.end_page_var, width=8)
        self.end_page_entry.pack(side=tk.LEFT)
        self.page_note_label = ttk.Label(self.page_frame, text=" 页")
        self.page_note_label.pack(side=tk.LEFT, padx=(5, 0))
        
        row += 1
        
        # 天数设置
        self.days_label = ttk.Label(settings_frame, text="下载天数:")
        self.days_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.days_frame = ttk.Frame(settings_frame)
        self.days_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)

        self.days_var = tk.StringVar(value=str(self.download_config.days))
        self.days_entry = ttk.Entry(self.days_frame, textvariable=self.days_var, width=8)
        self.days_entry.pack(side=tk.LEFT)
        self.days_note_label = ttk.Label(self.days_frame, text=" 天（日期模式）")
        self.days_note_label.pack(side=tk.LEFT, padx=(5, 0))

        row += 1

        # 关注画师子模式设置
        self.date_mode_label = ttk.Label(settings_frame, text="关注画师模式:")
        self.date_mode_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.date_mode_frame = ttk.Frame(settings_frame)
        self.date_mode_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)

        self.date_mode_var = tk.StringVar(value=self.download_config.date_mode.value)
        ttk.Radiobutton(
            self.date_mode_frame, text="按日期范围", variable=self.date_mode_var, value="by_date_range"
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            self.date_mode_frame, text="按页码范围", variable=self.date_mode_var, value="by_page_range"
        ).pack(side=tk.LEFT)

        row += 1
        
        # 排行榜配置框架
        self._setup_ranking_config_frame(settings_frame, row)
        
        # 为排行榜配置调整行数
        row += 6
        
        row += 1
        
        # 动图处理
        ttk.Label(settings_frame, text="动图处理:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.gif_mode_var = tk.StringVar(value=self.download_config.gif_mode.value)
        gif_frame = ttk.Frame(settings_frame)
        gif_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Radiobutton(
            gif_frame, text="仅GIF", variable=self.gif_mode_var, value="gif_only"
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            gif_frame, text="仅帧", variable=self.gif_mode_var, value="frames_only"
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            gif_frame, text="都保存", variable=self.gif_mode_var, value="both"
        ).pack(side=tk.LEFT)
        
        row += 1
        
        # 分类模式
        ttk.Label(settings_frame, text="文件分类:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.classify_mode_var = tk.StringVar(value=self.download_config.classify_mode.value)
        classify_frame = ttk.Frame(settings_frame)
        classify_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Radiobutton(
            classify_frame, text="按日期", variable=self.classify_mode_var, value="by_date"
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            classify_frame, text="按作者", variable=self.classify_mode_var, value="by_author"
        ).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(
            classify_frame, text="不分类", variable=self.classify_mode_var, value="flat"
        ).pack(side=tk.LEFT)
        
        row += 1
        
        # 画师新作保存路径
        self.date_path_label = ttk.Label(settings_frame, text="画师新作路径:")
        self.date_path_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.date_path_frame = ttk.Frame(settings_frame)
        self.date_path_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        self.date_path_frame.columnconfigure(0, weight=1)

        self.date_path_var = tk.StringVar(value=self.download_config.save_path)
        self.date_path_entry = ttk.Entry(self.date_path_frame, textvariable=self.date_path_var)
        self.date_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.date_path_button = ttk.Button(
            self.date_path_frame, text="浏览", command=self._browse_date_folder
        )
        self.date_path_button.grid(row=0, column=1, padx=(5, 0))

        row += 1

        # 搜索模式保存路径
        self.search_path_label = ttk.Label(settings_frame, text="搜索结果路径:")
        self.search_path_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.search_path_frame = ttk.Frame(settings_frame)
        self.search_path_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        self.search_path_frame.columnconfigure(0, weight=1)

        # 从配置中获取搜索路径，如果没有则使用默认值
        search_path = getattr(self.download_config, 'search_save_path', '') or "pixiv_search"
        self.search_path_var = tk.StringVar(value=search_path)
        self.search_path_entry = ttk.Entry(self.search_path_frame, textvariable=self.search_path_var)
        self.search_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.search_path_button = ttk.Button(
            self.search_path_frame, text="浏览", command=self._browse_search_folder
        )
        self.search_path_button.grid(row=0, column=1, padx=(5, 0))

        row += 1

        # 用户模式保存路径
        self.user_path_label = ttk.Label(settings_frame, text="用户作品路径:")
        self.user_path_label.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.user_path_frame = ttk.Frame(settings_frame)
        self.user_path_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        self.user_path_frame.columnconfigure(0, weight=1)

        # 从配置中获取用户路径，如果没有则使用默认值
        user_path = getattr(self.download_config, 'user_save_path', '') or "pixiv_users"
        self.user_path_var = tk.StringVar(value=user_path)
        self.user_path_entry = ttk.Entry(self.user_path_frame, textvariable=self.user_path_var)
        self.user_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.user_path_button = ttk.Button(
            self.user_path_frame, text="浏览", command=self._browse_user_folder
        )
        self.user_path_button.grid(row=0, column=1, padx=(5, 0))

        row += 1

        # 排行榜保存路径（移动到这里）
        self.ranking_path_label_bottom = ttk.Label(settings_frame, text="排行榜路径:")
        self.ranking_path_label_bottom.grid(row=row, column=0, sticky=tk.W, pady=2)
        self.ranking_path_frame_bottom = ttk.Frame(settings_frame)
        self.ranking_path_frame_bottom.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2)
        self.ranking_path_frame_bottom.columnconfigure(0, weight=1)

        # 使用排行榜专用路径变量
        ranking_path = self.download_config.ranking_config.custom_save_path or "pixiv_ranking"
        self.ranking_path_var = tk.StringVar(value=ranking_path)
        self.ranking_path_entry_bottom = ttk.Entry(self.ranking_path_frame_bottom, textvariable=self.ranking_path_var)
        self.ranking_path_entry_bottom.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.ranking_path_button_bottom = ttk.Button(
            self.ranking_path_frame_bottom, text="浏览", command=self._browse_ranking_folder
        )
        self.ranking_path_button_bottom.grid(row=0, column=1, padx=(5, 0))

    def _setup_performance_frame(self, parent):
        """设置性能设置框架"""
        perf_frame = ttk.LabelFrame(parent, text="性能设置", padding="10")
        perf_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        perf_frame.columnconfigure(1, weight=1)
        
        # 并发下载数
        ttk.Label(perf_frame, text="并发下载:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.concurrent_downloads_var = tk.StringVar(value=str(self.spider_config.concurrent_downloads))
        ttk.Entry(perf_frame, textvariable=self.concurrent_downloads_var, width=10).grid(
            row=0, column=1, sticky=tk.W, pady=2
        )
        
        # 最大工作线程
        ttk.Label(perf_frame, text="工作线程:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_workers_var = tk.StringVar(value=str(self.spider_config.max_workers))
        ttk.Entry(perf_frame, textvariable=self.max_workers_var, width=10).grid(
            row=1, column=1, sticky=tk.W, pady=2
        )
        
        # 无头模式
        self.headless_var = tk.BooleanVar(value=self.spider_config.selenium_headless)
        ttk.Checkbutton(
            perf_frame, 
            text="无头浏览器模式（更快但无界面）", 
            variable=self.headless_var
        ).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)
    
    def _setup_control_frame(self, parent):
        """设置控制按钮框架"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        self.start_btn = ttk.Button(
            control_frame, text="开始下载", command=self.start_download
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(
            control_frame, text="停止下载", command=self.stop_download, state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            control_frame, text="保存设置", command=self.save_settings
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            control_frame, text="清空日志", command=self.clear_log
        ).pack(side=tk.LEFT)
    
    def _setup_progress_frame(self, parent):
        """设置进度条框架"""
        progress_frame = ttk.Frame(parent)
        progress_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            variable=self.progress_var, 
            maximum=100
        )
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.progress_label = ttk.Label(progress_frame, text="就绪")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
    
    def _setup_stats_frame(self, parent):
        """设置统计信息框架"""
        stats_frame = ttk.LabelFrame(parent, text="下载统计", padding="10")
        stats_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_frame, text="等待开始...")
        self.stats_label.pack()
    
    def _setup_log_frame(self, parent):
        """设置日志框架"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="10")
        log_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def setup_callbacks(self):
        """设置回调函数"""
        self.spider.set_progress_callback(self.on_progress_update)
        self.spider.set_status_callback(self.on_status_update)
    
    def load_settings(self):
        """加载设置到界面"""
        try:
            # 重新加载配置
            self.download_config = self.config_manager.load_download_config()
            self.spider_config = self.config_manager.load_spider_config()
            
            # 更新排行榜配置到GUI变量
            if self.ranking_category_var:
                ranking_config = self.download_config.ranking_config
                self.ranking_category_var.set(ranking_config.category.value)
                self.ranking_rating_var.set(ranking_config.rating.value)
                self.ranking_period_var.set(ranking_config.period.value)
                self.ranking_date_var.set(ranking_config.specific_date or "")
                # 使用新的排行榜路径变量
                if hasattr(self, 'ranking_path_var'):
                    self.ranking_path_var.set(ranking_config.custom_save_path or "pixiv_ranking")
                self.ranking_auto_naming_var.set(ranking_config.auto_folder_naming)
            
            self.log("✅ 设置已加载")
        except Exception as e:
            self.log(f"⚠️ 加载设置失败: {e}")
    
    def save_settings(self):
        """保存当前设置"""
        try:
            # 更新下载配置
            self.download_config.download_mode = DownloadMode(self.download_mode_var.get())
            self.download_config.search_keyword = self.search_keyword_var.get()
            self.download_config.user_id = int(self.user_id_var.get()) if self.user_id_var.get() else None
            self.download_config.start_page = int(self.start_page_var.get())
            self.download_config.end_page = int(self.end_page_var.get())
            self.download_config.days = int(self.days_var.get())

            # 保存关注画师子模式设置
            if hasattr(self, 'date_mode_var'):
                from pixiv_spider.models.config import DateMode
                self.download_config.date_mode = DateMode(self.date_mode_var.get())

            # 保存搜索配置设置
            if hasattr(self, 'search_category_var'):
                from pixiv_spider.models.config import SearchCategory, SearchBookmarkCount, SearchContentMode, SearchMode
                self.download_config.search_config.category = SearchCategory(self.search_category_var.get())

                # 处理收藏值设置
                bookmark_value = self.search_bookmark_var.get()
                if bookmark_value == "不启用":
                    self.download_config.search_config.bookmark_count = SearchBookmarkCount.DISABLED
                else:
                    # 从"1000users入り"格式中提取数字
                    count_str = bookmark_value.replace("users入り", "")
                    count = int(count_str)
                    self.download_config.search_config.bookmark_count = SearchBookmarkCount(count)

                # 保存内容模式设置（使用搜索种类行中的内容模式）
                if hasattr(self, 'search_content_mode_var'):
                    self.download_config.search_config.content_mode = SearchContentMode(self.search_content_mode_var.get())

            # 搜索模式固定使用页码范围下载
            from pixiv_spider.models.config import SearchMode
            self.download_config.search_sub_mode = SearchMode.BY_PAGE_RANGE
            self.download_config.gif_mode = GifMode(self.gif_mode_var.get())
            self.download_config.classify_mode = ClassifyMode(self.classify_mode_var.get())

            # 保存各模式的独立路径设置
            if hasattr(self, 'date_path_var'):
                self.download_config.save_path = self.date_path_var.get()
            if hasattr(self, 'search_path_var'):
                self.download_config.search_save_path = self.search_path_var.get()
            if hasattr(self, 'user_path_var'):
                self.download_config.user_save_path = self.user_path_var.get()
            
            # 更新排行榜配置
            if self.ranking_category_var:
                # 通过value查找对应的enum
                for cat in RankingCategory:
                    if cat.value == self.ranking_category_var.get():
                        self.download_config.ranking_config.category = cat
                        break
                
                for rating in RankingRating:
                    if rating.value == self.ranking_rating_var.get():
                        self.download_config.ranking_config.rating = rating
                        break
                
                for period in RankingPeriod:
                    if period.value == self.ranking_period_var.get():
                        self.download_config.ranking_config.period = period
                        break
                
                self.download_config.ranking_config.specific_date = self.ranking_date_var.get() or None
                # 使用新的排行榜路径变量
                if hasattr(self, 'ranking_path_var'):
                    self.download_config.ranking_config.custom_save_path = self.ranking_path_var.get() or None
                self.download_config.ranking_config.auto_folder_naming = self.ranking_auto_naming_var.get()
            
            # 更新爬虫配置
            self.spider_config.concurrent_downloads = int(self.concurrent_downloads_var.get())
            self.spider_config.max_workers = int(self.max_workers_var.get())
            self.spider_config.selenium_headless = self.headless_var.get()
            
            # 保存配置
            self.config_manager.save_download_config(self.download_config)
            self.config_manager.save_spider_config(self.spider_config)
            
            # 更新爬虫的配置
            self.spider.update_download_config(self.download_config)
            self.spider.update_spider_config(self.spider_config)
            
            self.log("💾 设置已保存")
            
        except ValueError as e:
            messagebox.showerror("设置错误", f"设置值无效: {e}")
        except Exception as e:
            self.log(f"⚠️ 保存设置失败: {e}")
            messagebox.showerror("保存失败", f"保存设置时出错: {e}")
    
    def browse_folder(self):
        """浏览画师新作文件夹（保持向后兼容）"""
        folder = filedialog.askdirectory(initialdir=self.date_path_var.get())
        if folder:
            self.date_path_var.set(folder)

    def _browse_date_folder(self):
        """浏览画师新作文件夹"""
        folder = filedialog.askdirectory(initialdir=self.date_path_var.get())
        if folder:
            self.date_path_var.set(folder)

    def _browse_search_folder(self):
        """浏览搜索结果文件夹"""
        current_path = self.search_path_var.get() or "."
        folder = filedialog.askdirectory(initialdir=current_path)
        if folder:
            self.search_path_var.set(folder)

    def _browse_user_folder(self):
        """浏览用户作品文件夹"""
        current_path = self.user_path_var.get() or "."
        folder = filedialog.askdirectory(initialdir=current_path)
        if folder:
            self.user_path_var.set(folder)
    
    def check_login_status(self):
        """检查登录状态"""
        def check_thread():
            try:
                is_authenticated = self.spider.authenticate()
                self.is_authenticated = is_authenticated
                
                if is_authenticated:
                    self.root.after(0, lambda: self.login_status_label.config(
                        text="✅ 已登录", foreground="green"
                    ))
                    self.root.after(0, lambda: self.log("✅ 登录状态正常"))
                else:
                    self.root.after(0, lambda: self.login_status_label.config(
                        text="❌ 未登录", foreground="red"
                    ))
                    self.root.after(0, lambda: self.log("❌ 未登录，请点击重新登录"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.login_status_label.config(
                    text="❌ 检查失败", foreground="red"
                ))
                error_msg = str(e)
                self.root.after(0, lambda msg=error_msg: self.log(f"❌ 登录检查失败: {msg}"))
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def start_login(self):
        """启动登录流程"""
        try:
            self.login_status_label.config(text="🔄 登录中...", foreground="orange")
            self.log("🔐 开始登录流程...")

            # 首先检查是否已有有效cookies
            is_logged_in, cookies = self.spider.auth_service.check_login_status()

            if is_logged_in:
                self.log("✅ 找到现有cookie文件")
                success = self._initialize_spider_services(cookies)
                self.is_authenticated = success

                if success:
                    self.login_status_label.config(text="✅ 已登录", foreground="green")
                    self.log("✅ 登录成功！")
                else:
                    self.login_status_label.config(text="❌ 登录失败", foreground="red")
                    self.log("❌ 服务初始化失败")
                return

            # 没有cookies，显示登录对话框
            self.log("🔐 没有找到cookie文件，需要登录")
            success = self._show_login_dialog()
            self.is_authenticated = success

            if success:
                self.login_status_label.config(text="✅ 已登录", foreground="green")
                self.log("✅ 登录成功！")
            else:
                self.login_status_label.config(text="❌ 登录失败", foreground="red")
                self.log("❌ 登录失败，请重试")

        except Exception as e:
            self.login_status_label.config(text="❌ 登录失败", foreground="red")
            self.log(f"❌ 登录失败: {e}")

    def _show_login_dialog(self):
        """显示登录对话框"""
        try:
            from .login_dialog import LoginDialog

            dialog = LoginDialog(self.root, self.spider.auth_service)
            success, cookies = dialog.show()

            if success and cookies:
                return self._initialize_spider_services(cookies)
            else:
                return False

        except Exception as e:
            self.log(f"❌ 显示登录对话框失败: {e}")
            return False

    def _initialize_spider_services(self, cookies):
        """初始化爬虫服务"""
        try:
            self.spider._cookies = cookies

            from pixiv_spider.services.api_service import PixivApiService
            from pixiv_spider.services.download_service import DownloadService

            self.spider.api_service = PixivApiService(cookies, self.spider.config_manager)
            self.spider.download_service = DownloadService(self.spider.api_service, self.spider.config_manager)
            self.spider.download_service.set_progress_callback(self.spider._progress_callback)
            self.spider.download_service.set_status_callback(self.spider._status_callback)
            self.spider._is_authenticated = True
            return True
        except Exception as e:
            self.log(f"❌ 初始化服务失败: {e}")
            return False

    def start_download(self):
        """开始下载"""
        if self.is_running:
            return
        
        if not self.is_authenticated:
            messagebox.showwarning("未登录", "请先登录再开始下载")
            return
        
        # 保存当前设置
        self.save_settings()
        
        # 验证设置
        errors = self.download_config.validate()
        if errors:
            messagebox.showerror("配置错误", "\\n".join(errors))
            return
        
        # 开始下载
        self.is_running = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        # 重置统计
        self.stats = {
            'start_time': datetime.now(),
            'total': 0,
            'completed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 启动下载线程
        self.download_thread = threading.Thread(target=self._download_worker, daemon=True)
        self.download_thread.start()
        
        self.log("🚀 开始下载任务...")
    
    def stop_download(self):
        """停止下载并清理资源"""
        if not self.is_running:
            return
        
        self.log("⏹️ 正在停止下载并清理资源...")
        
        # 停止爬虫（包含资源清理）
        self.spider.stop_download()
        
        # 更新GUI状态
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.progress_label.config(text="已停止")
        
        # 重置统计信息
        self.stats = {
            'start_time': None,
            'total': 0,
            'completed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        self.update_stats_display()
        
        self.log("✅ 下载已停止，资源已清理")
    
    def _download_worker(self):
        """下载工作线程"""
        try:
            stats = self.spider.start_download()
            
            success_msg = f"📊 下载完成: 总计 {stats['total']}, 成功 {stats['success']}, 失败 {stats['failed']}, 跳过 {stats['skipped']}"
            self.root.after(0, lambda msg=success_msg: self.log(msg))
            
        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda msg=error_msg: self.log(f"❌ 下载过程中出错: {msg}"))
        finally:
            self.root.after(0, self._download_finished)
    
    def _download_finished(self):
        """下载完成后的清理工作"""
        # 确保清理资源（无论是正常完成还是被中断）
        if hasattr(self.spider, 'cleanup_resources'):
            self.spider.cleanup_resources()
        
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.progress_label.config(text="下载完成")
        
        self.log("🧹 资源清理完成")
    
    def on_progress_update(self, current: int, total: int, message: str = ""):
        """进度更新回调"""
        def update():
            if total > 0:
                progress = (current / total) * 100
                self.progress_var.set(progress)
                self.progress_label.config(text=f"{current}/{total} - {message}")
            
            # 更新统计
            self.stats['completed'] = current
            self.stats['total'] = total
            self.update_stats_display()
        
        self.root.after(0, update)
    
    def on_status_update(self, message: str):
        """状态更新回调"""
        self.root.after(0, lambda msg=message: self.log(msg))
    
    def update_stats_display(self):
        """更新统计显示"""
        if self.stats['start_time']:
            elapsed = datetime.now() - self.stats['start_time']
            elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
            
            stats_text = (
                f"运行时间: {elapsed_str} | "
                f"总计: {self.stats['total']} | "
                f"已完成: {self.stats['completed']} | "
                f"成功: {self.stats['success']} | "
                f"失败: {self.stats['failed']} | "
                f"跳过: {self.stats['skipped']}"
            )
        else:
            stats_text = "等待开始..."
        
        self.stats_label.config(text=stats_text)
    
    def log(self, message: str):
        """添加日志消息"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"

            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)
        except Exception as e:
            # 如果日志记录失败，至少打印到控制台
            print(f"日志记录失败: {e}, 原始消息: {message}")
        
        # 限制日志长度
        if self.log_text.index(tk.END).split('.')[0] != '1':
            lines = int(self.log_text.index(tk.END).split('.')[0])
            if lines > 1000:
                self.log_text.delete(1.0, f"{lines-800}.0")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("📝 日志已清空")
    
    def run(self):
        """运行GUI应用"""
        try:
            # 绑定窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 启动主循环
            self.root.mainloop()
            
        except KeyboardInterrupt:
            self.log("⚠️ 用户中断程序")
        except Exception as e:
            self.log(f"❌ GUI运行错误: {e}")
            messagebox.showerror("运行错误", f"GUI运行时出错: {e}")
    
    def on_closing(self):
        """窗口关闭时的清理工作"""
        try:
            # 如果正在下载，先停止
            if self.is_running:
                self.log("⏹️ 检测到窗口关闭，正在停止下载...")
                self.stop_download()
            
            # 清理爬虫资源
            if hasattr(self.spider, 'cleanup_resources'):
                self.spider.cleanup_resources()
            
            # 保存设置
            self.save_settings()
            
            self.log("👋 程序退出，资源已清理")
            
        except Exception as e:
            print(f"关闭时清理资源出错: {e}")
        finally:
            # 销毁窗口
            self.root.destroy()
    
    def _setup_ranking_config_frame(self, parent, start_row):
        """设置排行榜配置框架"""
        ranking_config = self.download_config.ranking_config

        # 存储排行榜相关组件的引用
        self.ranking_widgets = []

        # 排行榜分类
        self.ranking_category_label = ttk.Label(parent, text="排行榜分类:")
        self.ranking_category_label.grid(row=start_row, column=0, sticky=tk.W, pady=2)
        self.ranking_category_frame = ttk.Frame(parent)
        self.ranking_category_frame.grid(row=start_row, column=1, sticky=(tk.W, tk.E), pady=2)

        # 添加到排行榜组件列表
        self.ranking_widgets.extend([self.ranking_category_label, self.ranking_category_frame])
        
        self.ranking_category_var = tk.StringVar(value=ranking_config.category.value)
        self.ranking_category_combo = ttk.Combobox(self.ranking_category_frame, textvariable=self.ranking_category_var,
                                    values=[cat.value for cat in RankingCategory],
                                    state="readonly", width=15)
        self.ranking_category_combo.pack(side=tk.LEFT)
        self.ranking_category_combo.bind('<<ComboboxSelected>>', self._on_ranking_category_changed)

        # 年龄分级
        self.ranking_rating_var = tk.StringVar(value=ranking_config.rating.value)
        self.ranking_rating_combo = ttk.Combobox(self.ranking_category_frame, textvariable=self.ranking_rating_var,
                                  values=[rating.value for rating in RankingRating],
                                  state="readonly", width=10)
        self.ranking_rating_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # 时间周期
        self.ranking_period_label = ttk.Label(parent, text="时间周期:")
        self.ranking_period_label.grid(row=start_row+1, column=0, sticky=tk.W, pady=2)
        self.ranking_period_frame = ttk.Frame(parent)
        self.ranking_period_frame.grid(row=start_row+1, column=1, sticky=(tk.W, tk.E), pady=2)

        self.ranking_period_var = tk.StringVar(value=ranking_config.period.value)
        self.ranking_period_combo = ttk.Combobox(self.ranking_period_frame, textvariable=self.ranking_period_var,
                                   values=[period.value for period in RankingPeriod],
                                   state="readonly", width=15)
        self.ranking_period_combo.pack(side=tk.LEFT)

        # 添加到排行榜组件列表
        self.ranking_widgets.extend([self.ranking_period_label, self.ranking_period_frame])

        # 指定日期
        self.ranking_date_label = ttk.Label(parent, text="指定日期:")
        self.ranking_date_label.grid(row=start_row+2, column=0, sticky=tk.W, pady=2)
        self.ranking_date_frame = ttk.Frame(parent)
        self.ranking_date_frame.grid(row=start_row+2, column=1, sticky=(tk.W, tk.E), pady=2)

        self.ranking_date_var = tk.StringVar(value=ranking_config.specific_date or "")
        self.ranking_date_entry = ttk.Entry(self.ranking_date_frame, textvariable=self.ranking_date_var, width=15)
        self.ranking_date_entry.pack(side=tk.LEFT)
        self.ranking_date_note = ttk.Label(self.ranking_date_frame, text=" (格式: 20250725, 空白=最新)")
        self.ranking_date_note.pack(side=tk.LEFT, padx=(5, 0))

        # 添加到排行榜组件列表
        self.ranking_widgets.extend([self.ranking_date_label, self.ranking_date_frame])

        # 初始化时间周期选项
        self._update_ranking_period_options()

        # 初始化页码设置状态和组件显示
        self._update_page_settings()
        
        # 自动命名
        self.ranking_auto_naming_var = tk.BooleanVar(value=ranking_config.auto_folder_naming)
        ttk.Checkbutton(parent, text="自动按分类命名文件夹", 
                       variable=self.ranking_auto_naming_var).grid(
                           row=start_row+4, column=1, sticky=tk.W, pady=2)
    
    def _browse_ranking_folder(self):
        """浏览排行榜文件夹"""
        current_path = self.ranking_path_var.get() or "."
        folder = filedialog.askdirectory(initialdir=current_path)
        if folder:
            self.ranking_path_var.set(folder)

    def _on_ranking_category_changed(self, event=None):
        """排行榜分类变化时的回调"""
        self._update_ranking_period_options()

    def _on_download_mode_changed(self):
        """下载模式变化时的回调"""
        self._update_page_settings()

    def _update_page_settings(self):
        """根据下载模式更新页码设置和组件显示"""
        if not hasattr(self, 'start_page_entry'):
            return

        current_mode = self.download_mode_var.get()

        # 隐藏所有配置项
        self._hide_all_config_widgets()

        if current_mode == "ranking":
            # 排行榜模式：显示排行榜配置，固定页码1-2
            self._show_ranking_widgets()
            self.start_page_var.set("1")
            self.end_page_var.set("2")
            self.start_page_entry.config(state="disabled")
            self.end_page_entry.config(state="disabled")
            self.page_note_label.config(text=" 页 (排行榜固定1-2页)")
            self._show_page_widgets()

        elif current_mode == "date":
            # 日期模式：显示天数设置和页码范围
            self._show_days_widgets()
            self._show_page_widgets()
            self.start_page_entry.config(state="normal")
            self.end_page_entry.config(state="normal")
            self.page_note_label.config(text=" 页")

        elif current_mode == "search":
            # 搜索模式：显示搜索关键词和页码设置
            self._show_search_widgets()
            self._show_page_widgets()
            self.start_page_entry.config(state="normal")
            self.end_page_entry.config(state="normal")
            self.page_note_label.config(text=" 页")

        elif current_mode == "user":
            # 用户模式：显示用户ID和页码设置
            self._show_user_widgets()
            self._show_page_widgets()
            self.start_page_entry.config(state="normal")
            self.end_page_entry.config(state="normal")
            self.page_note_label.config(text=" 页")

    def _hide_all_config_widgets(self):
        """隐藏所有配置组件"""
        # 隐藏搜索关键词
        if hasattr(self, 'search_keyword_label'):
            self.search_keyword_label.grid_remove()
            self.search_keyword_entry.grid_remove()

        # 隐藏搜索配置
        if hasattr(self, 'search_category_label'):
            self.search_category_label.grid_remove()
            self.search_category_frame.grid_remove()



        # 隐藏用户ID
        if hasattr(self, 'user_id_label'):
            self.user_id_label.grid_remove()
            self.user_id_entry.grid_remove()

        # 隐藏页码范围
        if hasattr(self, 'page_range_label'):
            self.page_range_label.grid_remove()
            self.page_frame.grid_remove()

        # 隐藏天数设置
        if hasattr(self, 'days_label'):
            self.days_label.grid_remove()
            self.days_frame.grid_remove()

        # 隐藏关注画师子模式设置
        if hasattr(self, 'date_mode_label'):
            self.date_mode_label.grid_remove()
            self.date_mode_frame.grid_remove()

        # 隐藏排行榜配置
        if hasattr(self, 'ranking_widgets'):
            for widget in self.ranking_widgets:
                widget.grid_remove()

        # 隐藏所有路径设置
        if hasattr(self, 'date_path_label'):
            self.date_path_label.grid_remove()
            self.date_path_frame.grid_remove()

        if hasattr(self, 'search_path_label'):
            self.search_path_label.grid_remove()
            self.search_path_frame.grid_remove()

        if hasattr(self, 'user_path_label'):
            self.user_path_label.grid_remove()
            self.user_path_frame.grid_remove()

        if hasattr(self, 'ranking_path_label_bottom'):
            self.ranking_path_label_bottom.grid_remove()
            self.ranking_path_frame_bottom.grid_remove()

    def _show_search_widgets(self):
        """显示搜索相关组件"""
        if hasattr(self, 'search_keyword_label'):
            self.search_keyword_label.grid()
            self.search_keyword_entry.grid()
        if hasattr(self, 'search_category_label'):
            self.search_category_label.grid()
            self.search_category_frame.grid()

        if hasattr(self, 'search_path_label'):
            self.search_path_label.grid()
            self.search_path_frame.grid()

    def _show_user_widgets(self):
        """显示用户相关组件"""
        if hasattr(self, 'user_id_label'):
            self.user_id_label.grid()
            self.user_id_entry.grid()
        if hasattr(self, 'user_path_label'):
            self.user_path_label.grid()
            self.user_path_frame.grid()

    def _show_page_widgets(self):
        """显示页码相关组件"""
        if hasattr(self, 'page_range_label'):
            self.page_range_label.grid()
            self.page_frame.grid()

    def _show_days_widgets(self):
        """显示天数相关组件（关注画师模式）"""
        if hasattr(self, 'days_label'):
            self.days_label.grid()
            self.days_frame.grid()
        if hasattr(self, 'date_mode_label'):
            self.date_mode_label.grid()
            self.date_mode_frame.grid()
        if hasattr(self, 'date_path_label'):
            self.date_path_label.grid()
            self.date_path_frame.grid()

    def _show_ranking_widgets(self):
        """显示排行榜相关组件"""
        if hasattr(self, 'ranking_widgets'):
            for widget in self.ranking_widgets:
                widget.grid()
        if hasattr(self, 'ranking_path_label_bottom'):
            self.ranking_path_label_bottom.grid()
            self.ranking_path_frame_bottom.grid()

    def _update_ranking_period_options(self):
        """更新时间周期选项"""
        if not hasattr(self, 'ranking_period_combo'):
            return

        # 获取当前选择的分类
        current_category = None
        for cat in RankingCategory:
            if cat.value == self.ranking_category_var.get():
                current_category = cat
                break

        if not current_category:
            return

        # 创建临时配置对象来获取可用选项
        temp_config = RankingConfig(category=current_category)
        available_periods = temp_config.get_available_periods()

        # 更新下拉框选项
        period_values = [period.value for period in available_periods]
        self.ranking_period_combo['values'] = period_values

        # 如果当前选择的时间周期不在可用选项中，重置为第一个选项
        current_period = self.ranking_period_var.get()
        if current_period not in period_values:
            if period_values:
                self.ranking_period_var.set(period_values[0])


def create_gui(config_manager: Optional[ConfigManager] = None) -> PixivSpiderGUI:
    """
    创建GUI应用程序
    
    Args:
        config_manager: 配置管理器
        
    Returns:
        PixivSpiderGUI: GUI应用实例
    """
    return PixivSpiderGUI(config_manager) 