"""
作品处理器

负责作品数据的处理和转换
"""

import logging
from typing import List, Optional, Dict, Any, Set
from datetime import datetime

from ..models.artwork import Artwork, ArtworkType, ArtworkStatus
from ..interfaces.api_interface import IApiService


class ArtworkProcessor:
    """作品处理器"""
    
    def __init__(self, api_service: IApiService):
        """
        初始化作品处理器
        
        Args:
            api_service: API服务接口
        """
        self.api_service = api_service
        self.logger = logging.getLogger(__name__)
        self._artwork_cache: Dict[int, Optional[Artwork]] = {}
    
    def process_artwork_links_batch(self, artwork_links: Set[str], max_workers: int = 8) -> List[Artwork]:
        """
        批量处理作品链接
        
        Args:
            artwork_links: 作品链接集合
            max_workers: 最大并发数
            
        Returns:
            List[Artwork]: 处理成功的作品列表
        """
        if not artwork_links:
            return []
        
        self.logger.info(f"开始批量处理 {len(artwork_links)} 个作品链接")
        
        # 1. 提取所有作品ID
        artwork_ids = []
        link_to_id = {}
        
        for link in artwork_links:
            artwork_id = self._extract_artwork_id(link)
            if artwork_id:
                # 检查缓存
                if artwork_id not in self._artwork_cache:
                    artwork_ids.append(artwork_id)
                link_to_id[link] = artwork_id
        
        # 2. 批量获取作品详情
        if artwork_ids:
            self.logger.info(f"批量获取 {len(artwork_ids)} 个作品详情")
            details_batch = self.api_service.get_artwork_details_batch(artwork_ids, max_workers)
            
            # 3. 批量处理和缓存结果
            for artwork_id, detail_data in details_batch.items():
                if detail_data and 'body' in detail_data:
                    try:
                        artwork = self._create_artwork_from_detail_data(detail_data['body'])
                        self._artwork_cache[artwork_id] = artwork
                    except Exception as e:
                        self.logger.error(f"创建作品对象失败: {artwork_id}, 错误: {e}")
                        self._artwork_cache[artwork_id] = None
                else:
                    self._artwork_cache[artwork_id] = None
        
        # 4. 收集所有成功的作品
        artworks = []
        for link in artwork_links:
            artwork_id = link_to_id.get(link)
            if artwork_id and artwork_id in self._artwork_cache:
                artwork = self._artwork_cache[artwork_id]
                if artwork:
                    artworks.append(artwork)
        
        self.logger.info(f"批量处理完成，成功处理 {len(artworks)} 个作品")
        return artworks
    
    def _extract_artwork_id(self, link_or_data) -> Optional[int]:
        """
        从链接或数据中提取作品ID
        
        Args:
            link_or_data: 作品链接字符串或包含ID的数据字典
            
        Returns:
            Optional[int]: 作品ID，提取失败时返回None
        """
        import re
        
        try:
            if isinstance(link_or_data, str):
                # 从链接中提取ID
                match = re.search(r'/artworks/(\d+)', link_or_data)
                if match:
                    return int(match.group(1))
            elif isinstance(link_or_data, dict):
                # 从数据字典中提取ID
                if 'id' in link_or_data:
                    return int(link_or_data['id'])
                elif 'illust_id' in link_or_data:
                    return int(link_or_data['illust_id'])
            return None
        except (ValueError, AttributeError):
            return None
    
    def _create_artwork_from_detail_data(self, artwork_info: Dict[str, Any]) -> Artwork:
        """
        从详情数据创建作品对象
        
        Args:
            artwork_info: 作品详情数据
            
        Returns:
            Artwork: 作品对象
        """
        # 基本信息
        artwork_id = str(artwork_info.get('id', ''))
        title = artwork_info.get('title', '未知标题')
        description = artwork_info.get('description', '')
        
        # 作者信息
        author_name = artwork_info.get('userName', '未知作者')
        author_id = str(artwork_info.get('userId', ''))
        
        # 作品类型
        artwork_type = ArtworkType.ILLUSTRATION
        if artwork_info.get('illustType') == 2:
            artwork_type = ArtworkType.MANGA
        elif 'ugoiraMetadata' in artwork_info:
            artwork_type = ArtworkType.UGOIRA
        
        # 上传时间
        upload_date = None
        if 'uploadDate' in artwork_info:
            try:
                upload_date = datetime.fromisoformat(artwork_info['uploadDate'].replace('Z', '+00:00'))
            except:
                pass
        
        # 标签
        tags = []
        if 'tags' in artwork_info and 'tags' in artwork_info['tags']:
            tags = [tag.get('tag', '') for tag in artwork_info['tags']['tags']]
        
        # 统计信息
        view_count = artwork_info.get('viewCount', 0)
        bookmark_count = artwork_info.get('bookmarkCount', 0)
        like_count = artwork_info.get('likeCount', 0)
        
        # 创建作品对象
        artwork = Artwork(
            id=artwork_id,
            title=title,
            author_name=author_name,
            author_id=author_id,
            type=artwork_type,
            upload_date=upload_date,
            view_count=view_count,
            bookmark_count=bookmark_count,
            like_count=like_count,
            tags=tags,
            description=description,
            url=f"https://www.pixiv.net/artworks/{artwork_id}",
            status=ArtworkStatus.PENDING
        )
        
        return artwork
    
    def clear_cache(self) -> None:
        """清理作品缓存"""
        self._artwork_cache.clear()
        self.logger.info("作品缓存已清理")
    
    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self._artwork_cache)
