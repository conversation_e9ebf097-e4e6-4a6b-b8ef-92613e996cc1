"""
认证服务

负责处理Pixiv登录、Cookie管理和认证状态检查
"""

import os
import time
import webbrowser
import logging
from typing import Optional, Tuple, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from ..interfaces.auth_interface import IAuthService
from ..models.exceptions import AuthenticationError, NetworkError, SeleniumError
from ..config.config_manager import ConfigManager


class AuthService(IAuthService):
    """认证服务类"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化认证服务
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.driver: Optional[webdriver.Chrome] = None
        self.logger = logging.getLogger(__name__)
    
    def check_login_status(self) -> <PERSON><PERSON>[bool, Optional[Dict[str, Any]]]:
        """
        检查登录状态
        
        Returns:
            Tuple[bool, Optional[Dict]]: (是否已登录, Cookie数据)
        """
        try:
            cookies = self.config_manager.load_cookies()
            if cookies:
                self.logger.info(f"找到现有Cookie文件，包含 {len(cookies)} 个cookie")
                return True, cookies
            else:
                self.logger.info("未找到Cookie文件")
                return False, None
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False, None
    
    def open_login_page(self) -> bool:
        """
        打开Pixiv登录页面
        
        Returns:
            bool: 是否成功打开
        """
        login_url = "https://accounts.pixiv.net/login"
        try:
            self.logger.info(f"正在打开登录页面: {login_url}")
            webbrowser.open(login_url)
            return True
        except Exception as e:
            self.logger.error(f"无法打开浏览器: {e}")
            return False
    
    def start_interactive_login(self, use_cli_mode=True) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        启动交互式登录流程

        Args:
            use_cli_mode: 是否使用命令行模式（默认True，GUI环境应设为False）

        Returns:
            Tuple[bool, Optional[Dict]]: (是否成功, Cookie数据)
        """
        self.logger.info("开始交互式登录流程")

        try:
            if use_cli_mode:
                # 命令行模式：显示提示信息并等待用户输入
                if not self.open_login_page():
                    self.logger.warning("无法自动打开浏览器，请手动访问登录页面")

                print("\n🔐 检测到没有cookie文件，需要登录Pixiv")
                print("📝 即将打开Pixiv登录页面...")
                print("\n📋 请按照以下步骤操作:")
                print("1. 在打开的浏览器中登录你的Pixiv账号")
                print("2. 登录成功后，确保能正常访问Pixiv主页")
                print("3. 完成登录后，回到这里按回车键继续")

                input("\n⏳ 登录完成后，请按回车键继续...")

                # 使用命令行版本的cookie获取
                return self.capture_cookies_with_selenium_cli()
            else:
                # GUI模式：直接获取cookies，不等待用户输入
                return self.capture_cookies_with_selenium()

        except Exception as e:
            self.logger.error(f"交互式登录失败: {e}")
            raise AuthenticationError(f"交互式登录失败: {e}")
    
    def capture_cookies_with_selenium(self) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        使用Selenium获取cookies（GUI版本，不使用input）

        Returns:
            Tuple[bool, Optional[Dict]]: (是否成功, Cookie数据)
        """
        self.logger.info("开始使用Selenium获取cookies")

        try:
            # 设置Chrome选项
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1200,800')

            # 启动浏览器
            self.driver = webdriver.Chrome(options=options)
            self.driver.get('https://www.pixiv.net/')

            # 等待页面加载
            import time
            time.sleep(3)

            # 检查登录状态
            if self._check_login_in_browser():
                # 获取cookies
                cookies = self.driver.get_cookies()

                # 保存cookies
                self.config_manager.save_cookies(cookies)
                self.logger.info(f"成功保存 {len(cookies)} 个cookie")
                return True, cookies
            else:
                self.logger.error("检测到未登录状态")
                return False, None

        except Exception as e:
            self.logger.error(f"获取cookies失败: {e}")
            raise SeleniumError(f"获取cookies失败: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None

    def capture_cookies_with_selenium_cli(self) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        使用Selenium获取cookies（命令行版本，使用input等待）

        Returns:
            Tuple[bool, Optional[Dict]]: (是否成功, Cookie数据)
        """
        self.logger.info("开始使用Selenium获取cookies（命令行版本）")

        try:
            # 设置Chrome选项
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1200,800')

            # 启动浏览器
            self.driver = webdriver.Chrome(options=options)
            self.driver.get('https://www.pixiv.net/')

            print("📱 已打开Pixiv主页，请在浏览器中确认已登录")
            print("💡 如果还没登录，请在浏览器中登录后刷新页面")
            print("✅ 确认登录状态正常后，按回车键继续...")

            input()

            # 检查登录状态
            if self._check_login_in_browser():
                # 获取cookies
                cookies = self.driver.get_cookies()

                # 保存cookies
                self.config_manager.save_cookies(cookies)
                self.logger.info(f"成功保存 {len(cookies)} 个cookie")
                print(f"✅ 成功保存 {len(cookies)} 个cookie")
                return True, cookies
            else:
                self.logger.error("检测到未登录状态")
                print("❌ 检测到未登录状态，请重新登录")
                return False, None

        except Exception as e:
            self.logger.error(f"获取cookies失败: {e}")
            raise SeleniumError(f"获取cookies失败: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None

    def _check_login_in_browser(self) -> bool:
        """
        检查浏览器中的登录状态
        
        Returns:
            bool: 是否已登录
        """
        try:
            # 等待页面加载
            time.sleep(3)
            
            # 检查是否存在登录相关元素
            try:
                # 查找用户头像或导航栏中的用户相关元素
                user_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                    'a[data-gtm-value="header_user_menu"], '
                    'img[alt*="avatar"], '
                    '.user-icon, '
                    '[data-testid="header-user-menu"]'
                )
                
                if user_elements:
                    self.logger.info("检测到登录状态 - 找到用户元素")
                    print("✅ 检测到登录状态")
                    return True
                
                # 检查是否存在登录按钮（如果存在说明未登录）
                login_buttons = self.driver.find_elements(By.CSS_SELECTOR, 
                    'a[href*="login"], '
                    'button[data-gtm-value="header_login"]'
                )
                
                if login_buttons:
                    self.logger.warning("检测到登录按钮，可能未登录")
                    print("❌ 检测到登录按钮，可能未登录")
                    return False
                
                # 如果都没找到，尝试检查URL或其他方式
                current_url = self.driver.current_url
                if 'login' in current_url:
                    self.logger.warning("当前在登录页面")
                    print("❌ 当前在登录页面")
                    return False
                
                self.logger.info("无法确定登录状态，假设已登录")
                print("⚠️ 无法确定登录状态，假设已登录")
                return True
                
            except Exception as e:
                self.logger.warning(f"检查登录状态时出错: {e}")
                print(f"⚠️ 检查登录状态时出错: {e}")
                return True  # 出错时假设已登录
                
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def get_or_create_cookies(self, use_cli_mode=True) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        获取或创建cookies

        Args:
            use_cli_mode: 是否使用命令行模式（默认True，GUI环境应设为False）

        Returns:
            Tuple[bool, Optional[Dict]]: (是否成功, Cookie数据)
        """
        # 首先检查是否已有有效cookies
        is_logged_in, cookies = self.check_login_status()

        if is_logged_in:
            self.logger.info("使用现有Cookie文件")
            if use_cli_mode:
                print("✅ 找到现有cookie文件")
            return True, cookies

        # 没有cookies，启动登录流程
        self.logger.info("没有找到Cookie文件，启动登录流程")
        if use_cli_mode:
            print("🔐 没有找到cookie文件，需要登录")
        success, cookies = self.start_interactive_login(use_cli_mode)

        return success, cookies
    
    def validate_cookies(self, cookies: List[Dict[str, Any]]) -> bool:
        """
        验证cookies的有效性
        
        Args:
            cookies: Cookie列表
            
        Returns:
            bool: Cookies是否有效
        """
        if not cookies:
            return False
        
        try:
            # 检查关键cookie是否存在
            cookie_names = [cookie.get('name', '') for cookie in cookies]
            required_cookies = ['PHPSESSID', 'device_token']
            
            for required in required_cookies:
                if not any(required in name for name in cookie_names):
                    self.logger.warning(f"缺少关键cookie: {required}")
                    return False
            
            self.logger.info("Cookie验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"Cookie验证失败: {e}")
            return False
    
    def save_cookies(self, cookies: List[Dict[str, Any]]) -> bool:
        """
        保存cookies

        Args:
            cookies: cookie列表

        Returns:
            bool: 是否保存成功
        """
        try:
            self.config_manager.save_cookies(cookies)
            self.logger.info("Cookies保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存Cookies失败: {e}")
            return False

    def clear_cookies(self) -> bool:
        """
        清除保存的cookies

        Returns:
            bool: 是否清除成功
        """
        try:
            self.config_manager.clear_cookies()
            self.logger.info("已清除Cookie文件")
            return True
        except Exception as e:
            self.logger.error(f"清除Cookie失败: {e}")
            return False
            raise AuthenticationError(f"清除Cookie失败: {e}")
    
    def simple_login_check(self) -> bool:
        """
        简单的登录检查（用于命令行环境）
        
        Returns:
            bool: 是否已登录
        """
        print("🔍 检查登录状态...")
        
        # 检查是否已有cookie
        has_cookies, cookies = self.check_login_status()
        
        if has_cookies:
            print(f"✅ 找到现有登录信息 ({len(cookies)} 个cookie)")
            return True
        
        print("❌ 没有找到登录信息")
        
        # 提示用户登录
        self._prompt_manual_login()
        
        print("\n📝 说明:")
        print("由于这是简化版本，需要手动处理cookie。")
        print("建议使用完整版GUI程序进行首次登录。")
        print("完整版程序会自动获取和保存登录信息。")
        
        # 再次检查
        has_cookies, cookies = self.check_login_status()
        if has_cookies:
            print(f"✅ 检测到登录信息 ({len(cookies)} 个cookie)")
            return True
        else:
            print("⚠️ 仍未检测到登录信息")
            print("请使用完整版GUI程序进行登录，或手动放置cookie文件")
            return False
    
    def _prompt_manual_login(self) -> None:
        """提示用户手动登录"""
        print("\n" + "="*60)
        print("🔐 需要登录Pixiv账号")
        print("="*60)
        
        print("\n📋 请按照以下步骤操作:")
        print("1. 在浏览器中登录你的Pixiv账号")
        print("2. 登录成功后，确保能正常访问Pixiv主页")
        print("3. 保持浏览器打开状态")
        print("4. 完成登录后，回到这里继续")
        
        print("\n💡 提示:")
        print("- 如果已经登录过，可以直接继续")
        print("- 登录后程序会自动获取必要的认证信息")
        print("- 首次登录可能需要验证码或邮箱确认")
        
        # 打开登录页面
        self.open_login_page()
        
        print("\n⏳ 请在浏览器中完成登录...")
        input("登录完成后，按回车键继续...")
    
    def __del__(self):
        """析构函数，确保浏览器驱动被正确关闭"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass 