@echo off
title Pixiv Spider - Modern GUI Launcher

echo.
echo Pixiv Spider - Modern GUI Launcher
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found, please install Python 3.7+
    echo.
    echo Solutions:
    echo 1. Download and install Python from https://www.python.org/
    echo 2. Make sure to check "Add Python to PATH" during installation
    echo 3. Restart this script
    echo.
    pause
    exit /b 1
)

echo Python is installed
echo.

REM Check if dependencies are installed
echo Checking dependencies...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo CustomTkinter not installed, installing...
    pip install customtkinter>=5.2.0
    if errorlevel 1 (
        echo CustomTkinter installation failed
        echo Please run manually: pip install customtkinter
        pause
        exit /b 1
    )
    echo CustomTkinter installed successfully
)

echo Dependencies check completed
echo.

REM Launch modern GUI
echo Starting modern GUI...
python run_modern_gui.py

REM If program exits with error, pause to see error message
if errorlevel 1 (
    echo.
    echo Program exited with error
    pause
)

echo.
echo Program exited
pause
