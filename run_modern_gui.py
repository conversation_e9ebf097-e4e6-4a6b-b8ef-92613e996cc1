#!/usr/bin/env python3
"""
Pixiv Spider 现代化GUI启动脚本

使用CustomTkinter的现代化图形用户界面
"""

import sys
import os
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def main():
    """主函数"""
    print("🎨 Pixiv Spider - 现代化GUI启动器")
    print("=" * 50)
    
    try:
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        
        print("🔄 正在导入模块...")
        
        try:
            # 检查CustomTkinter是否可用
            import customtkinter as ctk
            print("✅ CustomTkinter 已安装")
            
            from pixiv_spider.gui import create_modern_gui
            from pixiv_spider.config import ConfigManager
        except ImportError as e:
            print(f"❌ 导入错误: {e}")
            print("\n详细错误信息:")
            import traceback
            traceback.print_exc()
            print("\n📋 可能的解决方案:")
            print("1. 检查是否安装了所有必需的依赖包")
            print("2. 运行: pip install -r requirements.txt")
            print("3. 确保 customtkinter 已安装: pip install customtkinter")
            print("4. 确保 tkinter 已安装")
            input("\n按回车键退出...")
            return
        
        print("✅ 模块导入成功")
        
        # 创建配置管理器
        print("🔧 正在初始化配置...")
        config_manager = ConfigManager()
        
        # 创建现代化GUI应用
        print("🚀 正在启动现代化GUI界面...")
        app = create_modern_gui(config_manager)
        
        print("🎉 现代化GUI启动成功！")
        print("💡 提示: 首次使用请点击'重新登录'按钮进行登录")
        print("🎨 享受全新的现代化界面体验！")
        print()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")


if __name__ == "__main__":
    main()
