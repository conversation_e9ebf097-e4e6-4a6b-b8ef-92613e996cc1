"""
文件服务

负责文件操作和管理
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Optional, List
from ..utils.file_utils import FileUtils


class FileService:
    """文件服务类"""
    
    def __init__(self):
        """初始化文件服务"""
        self.logger = logging.getLogger(__name__)
    
    def ensure_directory(self, path: str) -> bool:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 是否成功
        """
        return FileUtils.ensure_directory(path)
    
    def safe_filename(self, filename: str, max_length: int = 255) -> str:
        """
        生成安全的文件名
        
        Args:
            filename: 原始文件名
            max_length: 最大长度
            
        Returns:
            str: 安全的文件名
        """
        return FileUtils.safe_filename(filename, max_length)
    
    def get_file_size(self, file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小（字节）
        """
        return FileUtils.get_file_size(file_path)
    
    def copy_file(self, src: str, dst: str, progress_callback=None) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            progress_callback: 进度回调函数
            
        Returns:
            bool: 是否成功
        """
        return FileUtils.copy_file_with_progress(src, dst, progress_callback)
    
    def move_file(self, src: str, dst: str) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            bool: 是否成功
        """
        return FileUtils.move_file(src, dst)
    
    def delete_file(self, file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功
        """
        return FileUtils.delete_file(file_path)
    
    def clean_empty_directories(self, root_path: str) -> int:
        """
        清理空目录
        
        Args:
            root_path: 根目录路径
            
        Returns:
            int: 删除的目录数量
        """
        return FileUtils.clean_empty_directories(root_path)
    
    def format_file_size(self, size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化后的大小字符串
        """
        return FileUtils.format_file_size(size_bytes) 