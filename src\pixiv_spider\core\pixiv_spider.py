"""
Pixiv爬虫核心类

这是重构后的主要爬虫类，采用依赖注入和服务层模式
"""

import logging
import os
import re
import time
import asyncio
import concurrent.futures
from datetime import datetime, timedelta
from typing import List, Optional, Callable, Dict, Any, Set, Union
from pathlib import Path
from collections import OrderedDict

from ..models.artwork import Artwork, ArtworkPage, ArtworkType, ArtworkStatus
from ..models.config import DownloadConfig, SpiderConfig, DownloadMode
from ..models.exceptions import PixivSpiderError, AuthenticationError
from ..interfaces.auth_interface import IAuthService
from ..interfaces.api_interface import IApiService
from ..interfaces.download_interface import IDownloadService
from ..container.service_container import ServiceContainer
from ..container.service_registry import ServiceRegistry
from ..utils.selenium_utils import SeleniumDriver
from ..config.config_manager import ConfigManager


class LRUCache:
    """简单的LRU缓存实现"""

    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()

    def get(self, key):
        if key in self.cache:
            # 移动到末尾（最近使用）
            self.cache.move_to_end(key)
            return self.cache[key]
        return None

    def put(self, key, value):
        if key in self.cache:
            # 更新现有键
            self.cache.move_to_end(key)
        elif len(self.cache) >= self.max_size:
            # 移除最久未使用的项
            self.cache.popitem(last=False)
        self.cache[key] = value

    def clear(self):
        self.cache.clear()

    def size(self):
        return len(self.cache)


class PixivSpider:
    """Pixiv爬虫主类"""

    def __init__(self, container_or_config=None):
        """
        初始化爬虫

        Args:
            container_or_config: 服务容器或配置管理器，如果为None则创建默认实例
        """
        self.logger = logging.getLogger(__name__)

        # 处理不同类型的输入参数
        if isinstance(container_or_config, ConfigManager):
            # 如果传入的是ConfigManager，创建容器并注册
            self.config_manager = container_or_config
            self.container = ServiceContainer()
            self.registry = ServiceRegistry(self.container)
            # 将ConfigManager注册到容器中
            self.container.register_singleton('config_manager', self.config_manager)
        elif isinstance(container_or_config, ServiceContainer):
            # 如果传入的是ServiceContainer
            self.container = container_or_config
            self.registry = ServiceRegistry(self.container)
            self.config_manager = self.container.get_config_manager()
        else:
            # 如果为None或其他，创建默认实例
            self.container = ServiceContainer()
            self.registry = ServiceRegistry(self.container)
            self.config_manager = self.container.get_config_manager()

        self._services_registered = False

        # 加载配置
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()

        # 延迟初始化服务
        self.auth_service: Optional[IAuthService] = None
        self.api_service: Optional[IApiService] = None
        self.download_service: Optional[IDownloadService] = None
        self.selenium_driver: Optional[SeleniumDriver] = None
        
        # 回调函数
        self._progress_callback: Optional[Callable] = None
        self._status_callback: Optional[Callable] = None

        # 作品处理缓存（避免重复处理相同作品）- 使用LRU缓存
        self._artwork_cache = LRUCache(max_size=self.spider_config.max_cache_size if hasattr(self.spider_config, 'max_cache_size') else 1000)
        
        # 状态
        self._is_running = False
        self._is_authenticated = False
        self._cookies: Optional[Dict[str, Any]] = None

    def _ensure_services_registered(self) -> None:
        """确保服务已注册（延迟初始化）"""
        if not self._services_registered:
            self.registry.register_all_services()
            self.auth_service = self.container.get('auth_service')
            self._services_registered = True
            self.logger.debug("服务注册完成")
    
    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self._progress_callback = callback
    
    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self._status_callback = callback
    
    def authenticate(self) -> bool:
        """
        进行身份验证

        Returns:
            bool: 是否验证成功
        """
        try:
            # 确保服务已注册
            self._ensure_services_registered()

            # 尝试加载已保存的Cookie
            success, cookies = self.auth_service.check_login_status()
            
            if success and cookies:
                self._cookies = cookies

                # 使用服务注册器创建配置好的服务
                self.api_service = self.registry.create_configured_api_service(cookies)
                self.download_service = self.registry.create_configured_download_service(self.api_service)

                # 设置回调函数
                self.download_service.set_progress_callback(self._progress_callback)
                self.download_service.set_status_callback(self._status_callback)
                
                self._is_authenticated = True
                self.logger.info("使用已保存的Cookie登录成功")
                return True
            
            # 如果没有有效Cookie，需要重新登录
            self.logger.warning("未找到有效的登录信息，需要重新登录")
            return False
            
        except Exception as e:
            self.logger.error(f"身份验证失败: {e}")
            return False
    
    def interactive_login(self) -> bool:
        """
        交互式登录

        Returns:
            bool: 是否登录成功
        """
        try:
            # 确保服务已注册
            self._ensure_services_registered()

            success, cookies = self.auth_service.get_or_create_cookies()
            
            if success and cookies:
                self._cookies = cookies
                
                # 使用服务注册器创建配置好的服务
                self.api_service = self.registry.create_configured_api_service(cookies)
                self.download_service = self.registry.create_configured_download_service(self.api_service)

                # 设置回调函数
                self.download_service.set_progress_callback(self._progress_callback)
                self.download_service.set_status_callback(self._status_callback)
                
                self._is_authenticated = True
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"交互式登录失败: {e}")
            return False
    
    def start_download(self) -> Dict[str, Any]:
        """
        开始下载
        
        Returns:
            Dict[str, Any]: 下载结果统计
        """
        if self._is_running:
            raise PixivSpiderError("爬虫已在运行中")
        
        if not self._is_authenticated:
            raise AuthenticationError("未登录，无法开始下载")
        
        try:
            # 重置状态，确保干净的启动环境
            self.reset_for_new_download()
            
            self._is_running = True
            self._notify_status("开始下载...")
            
            # 验证配置
            errors = self.download_config.validate()
            if errors:
                raise PixivSpiderError(f"配置验证失败: {', '.join(errors)}")
            
            # 确保目录存在
            self.config_manager.ensure_directories()
            
            # 根据下载模式执行不同的下载逻辑
            artworks = self._get_artworks_by_mode()
            
            if not artworks:
                self._notify_status("没有找到符合条件的作品")
                return {'total': 0, 'success': 0, 'failed': 0, 'skipped': 0}
            
            self.logger.info(f"找到 {len(artworks)} 个作品，开始下载")
            self._notify_status(f"找到 {len(artworks)} 个作品，开始下载...")
            
            # 设置下载路径并执行下载
            stats = self._execute_download_with_path(artworks)
            
            self._notify_status("下载任务完成")
            return stats
            
        except Exception as e:
            self.logger.error(f"下载过程中发生错误: {e}")
            self._notify_status(f"下载失败: {e}")
            raise
        finally:
            self._is_running = False
    
    def _get_artworks_by_mode(self) -> List[Artwork]:
        """根据下载模式获取作品列表"""
        mode = self.download_config.download_mode
        
        if mode == DownloadMode.DATE:
            return self._get_artworks_by_date()
        elif mode == DownloadMode.RANKING:
            return self._get_artworks_by_ranking()
        elif mode == DownloadMode.SEARCH:
            return self._get_artworks_by_search()
        elif mode == DownloadMode.USER:
            return self._get_artworks_by_user()
        elif mode == DownloadMode.BOOKMARK:
            return self._get_artworks_by_bookmark()
        elif mode == DownloadMode.FOLLOW:
            return self._get_artworks_by_follow()
        else:
            raise PixivSpiderError(f"不支持的下载模式: {mode}")
    
    def _ensure_selenium_driver(self):
        """确保Selenium驱动器已初始化"""
        if not self.selenium_driver:
            if not self._is_authenticated:
                self.logger.error("未登录，无法初始化Selenium驱动器")
                return False
            self.selenium_driver = SeleniumDriver(self._cookies, self.config_manager)
            self.logger.debug("Selenium驱动器已初始化")
        return True

    def _extract_artwork_id(self, link_or_data) -> Optional[int]:
        """
        从链接或数据中提取作品ID

        Args:
            link_or_data: 作品链接字符串或包含ID的数据字典

        Returns:
            Optional[int]: 作品ID，提取失败时返回None
        """
        try:
            if isinstance(link_or_data, str):
                # 从链接中提取ID
                match = re.search(r'/artworks/(\d+)', link_or_data)
                if match:
                    return int(match.group(1))
            elif isinstance(link_or_data, dict):
                # 从数据字典中提取ID
                if 'id' in link_or_data:
                    return int(link_or_data['id'])
                elif 'illust_id' in link_or_data:
                    return int(link_or_data['illust_id'])
            return None
        except (ValueError, AttributeError):
            return None

    def _get_download_path_for_mode(self) -> str:
        """根据下载模式获取对应的下载路径"""
        mode = self.download_config.download_mode

        if mode == DownloadMode.RANKING:
            return self._get_ranking_save_path()
        elif mode == DownloadMode.SEARCH:
            return self._get_search_save_path()
        elif mode == DownloadMode.USER:
            return self._get_user_save_path()
        else:
            # 其他模式使用默认路径
            return self.download_config.save_path

    def _execute_download_with_path(self, artworks: List[Artwork]) -> Dict[str, Any]:
        """
        使用正确的路径执行下载

        Args:
            artworks: 作品列表

        Returns:
            Dict[str, Any]: 下载统计信息
        """
        download_path = self._get_download_path_for_mode()
        self.logger.info(f"下载路径: {download_path}")

        # 临时修改路径配置
        original_save_path = self.download_config.save_path
        try:
            if download_path != original_save_path:
                self.download_config.save_path = download_path
                self.download_service.download_config.save_path = download_path

            return self.download_service.batch_download(artworks)
        finally:
            # 恢复原始路径
            self.download_config.save_path = original_save_path
            self.download_service.download_config.save_path = original_save_path

    def _get_artworks_by_date(self) -> List[Artwork]:
        """按日期获取作品 - 从关注画师新作品页面收集"""
        try:
            # 确保Selenium驱动器已初始化
            if not self._ensure_selenium_driver():
                return []

            # 根据子模式选择不同的处理逻辑
            from pixiv_spider.models.config import DateMode
            date_mode = getattr(self.download_config, 'date_mode', DateMode.BY_DATE_RANGE)

            if date_mode == DateMode.BY_PAGE_RANGE:
                return self._get_artworks_by_page_range()
            else:  # DateMode.BY_DATE_RANGE
                return self._get_artworks_by_date_range()

        except Exception as e:
            self.logger.error(f"按日期获取作品失败: {e}")
            return []

    def _get_artworks_by_page_range(self) -> List[Artwork]:
        """按页码范围下载 - 下载指定页码范围内的所有作品"""
        artworks = []

        # 生成关注画师新作品页面URL列表
        page_urls = []
        for page in range(self.download_config.start_page, self.download_config.end_page + 1):
            url = f"https://www.pixiv.net/bookmark_new_illust.php?p={page}"
            page_urls.append(url)

        self.logger.info(f"按页码范围模式: 准备从关注画师新作品页面采集 {len(page_urls)} 个页面...")

        # 获取作品链接
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls)

        self.logger.info(f"采集到 {len(artwork_links)} 个作品链接")

        if not artwork_links:
            self._log_no_artworks_warning()
            return []

        # 并行处理作品链接（不进行日期过滤）
        artworks = self._process_artwork_links_parallel(artwork_links)

        self.logger.info(f"按页码范围模式: 成功处理 {len(artworks)} 个作品")
        return artworks

    def _get_artworks_by_date_range(self) -> List[Artwork]:
        """按日期范围下载 - 在页码范围内按日期过滤作品"""
        artworks = []

        # 计算日期范围
        today = datetime.now().date()
        target_dates = []
        for i in range(self.download_config.days):
            target_date = today - timedelta(days=i)
            target_dates.append(target_date)

        self.logger.info(f"按日期范围模式: 目标下载日期: {[d.strftime('%Y-%m-%d') for d in target_dates]}")

        # 生成关注画师新作品页面URL列表
        page_urls = []
        for page in range(self.download_config.start_page, self.download_config.end_page + 1):
            url = f"https://www.pixiv.net/bookmark_new_illust.php?p={page}"
            page_urls.append(url)

        self.logger.info(f"按日期范围模式: 准备从关注画师新作品页面采集 {len(page_urls)} 个页面...")

        # 获取作品链接
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls)

        self.logger.info(f"采集到 {len(artwork_links)} 个作品链接")

        if not artwork_links:
            self._log_no_artworks_warning()
            return []

        # 并行处理作品链接并进行日期过滤
        all_artworks = self._process_artwork_links_parallel(artwork_links)

        processed_count = len(all_artworks)
        filtered_count = 0

        for artwork in all_artworks:
            self.logger.debug(f"处理作品 {artwork.id}: {artwork.title}, 上传日期: {artwork.upload_date}")

            # 检查日期过滤
            if artwork.upload_date:
                # 将artwork的上传日期转换为本地日期（忽略时区）
                if hasattr(artwork.upload_date, 'date'):
                    artwork_date = artwork.upload_date.date()
                else:
                    artwork_date = artwork.upload_date

                # 检查是否在目标日期列表中
                if artwork_date in target_dates:
                    artworks.append(artwork)
                    self.logger.debug(f"作品 {artwork.id} 通过日期过滤: {artwork_date}")
                else:
                    filtered_count += 1
                    self.logger.debug(f"作品 {artwork.id} 被日期过滤: {artwork_date} 不在目标日期 {target_dates} 中")
            else:
                # 如果没有上传日期信息，也包含该作品（可能是数据解析问题）
                artworks.append(artwork)
                self.logger.warning(f"作品 {artwork.id} 没有上传日期信息，但仍包含在结果中")

        self.logger.info(f"按日期范围模式: 成功解析 {processed_count} 个作品, 日期过滤掉 {filtered_count} 个")
        self.logger.info(f"找到 {len(artworks)} 个符合日期条件的作品")
        return artworks

    def _log_no_artworks_warning(self):
        """记录没有获取到作品的警告信息"""
        self.logger.warning("没有获取到任何作品链接，可能的原因：")
        self.logger.warning("1. 您还没有关注任何画师")
        self.logger.warning("2. 关注的画师最近没有发布新作品")
        self.logger.warning("3. 登录状态可能已过期，请尝试重新登录")
        self.logger.warning("4. 页面加载失败或网络问题")
    
    def _get_artworks_by_ranking(self) -> List[Artwork]:
        """按排行榜获取作品 - 使用Selenium访问排行榜页面"""
        artworks = []
        
        try:
            ranking_config = self.download_config.ranking_config
            
            # 确保Selenium驱动器已初始化
            if not self._ensure_selenium_driver():
                return []
            
            # 验证排行榜配置
            config_errors = ranking_config.validate_config()
            if config_errors:
                error_msg = "排行榜配置错误: " + "; ".join(config_errors)
                self.logger.error(error_msg)
                raise PixivSpiderError(error_msg)

            self.logger.info(f"准备采集排行榜: {ranking_config.category.value}-{ranking_config.rating.value}-{ranking_config.period.value}")

            # 排行榜模式固定使用1-2页
            ranking_start_page = 1
            ranking_end_page = 2
            self.logger.info(f"排行榜模式使用固定页码范围: {ranking_start_page}-{ranking_end_page}")

            # 生成排行榜页面URL列表
            page_urls = []
            for page in range(ranking_start_page, ranking_end_page + 1):
                # 使用新的URL构建方法
                url = ranking_config.get_ranking_url(page=page, date=ranking_config.specific_date)
                page_urls.append(url)
                self.logger.debug(f"排行榜URL: {url}")
            
            self.logger.info(f"准备从排行榜页面采集 {len(page_urls)} 个页面...")
            
            # 获取作品链接
            artwork_links = self.selenium_driver.get_page_links_batch(page_urls)
            
            self.logger.info(f"采集到 {len(artwork_links)} 个排行榜作品链接")
            
            if not artwork_links:
                self.logger.warning("没有获取到任何排行榜作品链接，可能的原因：")
                self.logger.warning("1. 排行榜分类参数错误")
                self.logger.warning("2. 指定日期没有排行榜数据")
                self.logger.warning("3. 网络问题或页面加载失败")
                self.logger.warning("4. 排行榜页面访问限制")
                if ranking_config.rating.value == "R-18":
                    self.logger.warning("5. R18内容需要确认登录状态和年龄验证")
                return []
            
            # 处理每个作品链接
            for link in artwork_links:
                artwork = self._process_artwork_link(link)
                if artwork:
                    artworks.append(artwork)
            
            self.logger.info(f"成功处理 {len(artworks)} 个排行榜作品")
            return artworks
            
        except Exception as e:
            self.logger.error(f"按排行榜获取作品失败: {e}")
            return []
    

    
    def _get_ranking_save_path(self) -> str:
        """获取排行榜的保存路径"""
        ranking_config = self.download_config.ranking_config
        
        # 使用自定义路径或默认路径
        base_path = ranking_config.custom_save_path or self.download_config.save_path
        
        # 如果启用自动命名，添加分类文件夹
        if ranking_config.auto_folder_naming:
            folder_name = ranking_config.get_folder_name()
            save_path = os.path.join(base_path, folder_name)
        else:
            save_path = base_path
        
        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)
        
        return save_path

    def _get_search_save_path(self) -> str:
        """获取搜索的保存路径"""
        # 使用搜索专用路径或默认路径
        if self.download_config.search_save_path:
            save_path = self.download_config.search_save_path
        else:
            save_path = self.download_config.save_path

        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)

        return save_path

    def _get_user_save_path(self) -> str:
        """获取用户的保存路径"""
        # 使用用户专用路径或默认路径
        if self.download_config.user_save_path:
            save_path = self.download_config.user_save_path
        else:
            save_path = self.download_config.save_path

        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)

        return save_path

    def _process_artwork_links_parallel(self, artwork_links: Set[str], max_workers: int = None) -> List[Artwork]:
        """
        并行处理作品链接（优化版本）

        Args:
            artwork_links: 作品链接集合
            max_workers: 最大并发数

        Returns:
            List[Artwork]: 处理成功的作品列表
        """
        if not artwork_links:
            return []

        # 使用配置中的并发数，如果没有指定则使用默认值
        if max_workers is None:
            max_workers = self.spider_config.max_workers

        self.logger.info(f"开始优化并行处理 {len(artwork_links)} 个作品链接，并发数: {max_workers}")

        # 1. 提取所有作品ID
        artwork_ids = []
        link_to_id = {}

        for link in artwork_links:
            artwork_id = self._extract_artwork_id(link)
            if artwork_id:
                # 检查缓存
                if self._artwork_cache.get(artwork_id) is None:
                    artwork_ids.append(artwork_id)
                link_to_id[link] = artwork_id

        # 2. 批量获取作品详情（未缓存的）
        if artwork_ids:
            self.logger.info(f"批量获取 {len(artwork_ids)} 个作品详情")
            # 分批处理，避免单次请求过多
            batch_size = min(50, max_workers * 5)  # 每批最多50个，或并发数的5倍
            details_batch = {}

            for i in range(0, len(artwork_ids), batch_size):
                batch_ids = artwork_ids[i:i + batch_size]
                batch_results = self.api_service.get_artwork_details_batch(batch_ids, max_workers)
                details_batch.update(batch_results)

                # 批次间短暂延迟，避免过于频繁的请求
                if i + batch_size < len(artwork_ids):
                    time.sleep(0.2)

            # 3. 批量处理和缓存结果
            for artwork_id, detail_data in details_batch.items():
                if detail_data and 'body' in detail_data:
                    try:
                        artwork = self._create_artwork_from_detail_data(detail_data['body'])
                        if not self._should_filter_artwork(artwork):
                            self._artwork_cache.put(artwork_id, artwork)
                        else:
                            self._artwork_cache.put(artwork_id, None)
                    except Exception as e:
                        self.logger.error(f"创建作品对象失败: {artwork_id}, 错误: {e}")
                        self._artwork_cache.put(artwork_id, None)
                else:
                    self._artwork_cache.put(artwork_id, None)

        # 4. 收集所有成功的作品
        artworks = []
        for link in artwork_links:
            artwork_id = link_to_id.get(link)
            if artwork_id:
                artwork = self._artwork_cache.get(artwork_id)
                if artwork:
                    artworks.append(artwork)

        self.logger.info(f"优化并行处理完成，成功处理 {len(artworks)} 个作品")
        return artworks



    def _get_artworks_by_search(self) -> List[Artwork]:
        """按搜索获取作品 - 使用页码范围下载"""
        try:
            keyword = self.download_config.search_keyword
            if not keyword:
                raise PixivSpiderError("搜索模式下必须提供搜索关键词")

            # 搜索模式直接使用页码范围下载
            return self._get_artworks_by_search_page_range(keyword)

        except Exception as e:
            self.logger.error(f"按搜索获取作品失败: {e}")
            return []

    def _get_artworks_by_search_page_range(self, keyword: str) -> List[Artwork]:
        """按页码范围搜索下载 - 使用新的URL生成逻辑"""
        artworks = []

        # 确保Selenium驱动器已初始化
        if not self._ensure_selenium_driver():
            return []

        # 生成搜索页面URL列表
        page_urls = []
        search_config = self.download_config.search_config

        for page in range(self.download_config.start_page, self.download_config.end_page + 1):
            url = search_config.get_search_url(keyword, page)
            page_urls.append(url)

        self.logger.info(f"按页码范围搜索模式: 准备从搜索页面采集 {len(page_urls)} 个页面...")
        self.logger.info(f"搜索关键词: {keyword}")
        self.logger.info(f"搜索种类: {search_config.category.value}")
        self.logger.info(f"收藏过滤: {search_config.bookmark_count.value if search_config.bookmark_count.value != -1 else '不启用'}")

        # 获取作品链接
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls)

        self.logger.info(f"采集到 {len(artwork_links)} 个作品链接")

        if not artwork_links:
            self.logger.warning("没有获取到任何作品链接，可能的原因：")
            self.logger.warning("1. 搜索关键词没有匹配的作品")
            self.logger.warning("2. 收藏过滤条件过于严格")
            self.logger.warning("3. 页面加载失败或网络问题")
            self.logger.warning("4. 搜索关键词需要为英文或日文")
            return []

        # 并行处理作品链接（不进行额外过滤）
        artworks = self._process_artwork_links_parallel(artwork_links)

        self.logger.info(f"按页码范围搜索模式: 成功处理 {len(artworks)} 个作品")
        return artworks


    
    def _get_artworks_by_user(self) -> List[Artwork]:
        """按用户获取作品"""
        artworks = []
        
        try:
            user_id = self.download_config.user_id
            if not user_id:
                raise PixivSpiderError("用户模式下必须提供用户ID")
            
            # 确保Selenium驱动器已初始化
            if not self._ensure_selenium_driver():
                return []
            
            pages = self.download_config.end_page - self.download_config.start_page + 1
            artwork_links = self.selenium_driver.get_user_artworks(user_id, pages)
            
            # 并行处理作品链接
            artworks = self._process_artwork_links_parallel(artwork_links)
            
            return artworks
            
        except Exception as e:
            self.logger.error(f"按用户获取作品失败: {e}")
            return []
    
    def _get_artworks_by_bookmark(self) -> List[Artwork]:
        """按收藏获取作品"""
        # TODO: 实现收藏模式
        self.logger.warning("收藏模式尚未实现")
        return []
    
    def _get_artworks_by_follow(self) -> List[Artwork]:
        """按关注获取作品"""
        # TODO: 实现关注模式
        self.logger.warning("关注模式尚未实现")
        return []
    
    def _process_artwork_link(self, link: str) -> Optional[Artwork]:
        """处理作品链接，获取作品信息"""
        try:
            # 从链接中提取作品ID
            artwork_id = self._extract_artwork_id(link)
            if not artwork_id:
                return None

            # 检查缓存
            if artwork_id in self._artwork_cache:
                return self._artwork_cache[artwork_id]

            # 获取作品详情
            detail_data = self.api_service.get_artwork_detail(artwork_id)
            if not detail_data or 'body' not in detail_data:
                # 缓存失败结果
                self._artwork_cache[artwork_id] = None
                return None

            artwork_info = detail_data['body']
            
            # 创建作品对象
            artwork = self._create_artwork_from_detail_data(artwork_info)
            
            # 应用过滤条件
            if self._should_filter_artwork(artwork):
                # 缓存过滤结果
                self._artwork_cache[artwork_id] = None
                return None

            # 缓存成功结果
            self._artwork_cache[artwork_id] = artwork
            return artwork
            
        except Exception as e:
            self.logger.error(f"处理作品链接失败: {link}, 错误: {e}")
            # 缓存失败结果
            if 'artwork_id' in locals():
                self._artwork_cache[artwork_id] = None
            return None

    def clear_artwork_cache(self):
        """清理作品缓存"""
        cache_size = self._artwork_cache.size()
        self._artwork_cache.clear()
        self.logger.info(f"作品缓存已清理，释放了 {cache_size} 个缓存项")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': self._artwork_cache.size(),
            'max_cache_size': self._artwork_cache.max_size,
            'cache_usage_percent': (self._artwork_cache.size() / self._artwork_cache.max_size) * 100
        }
    
    def _create_artwork_from_detail_data(self, data: Dict[str, Any]) -> Artwork:
        """从详情数据创建作品对象"""
        # 解析上传日期
        upload_date = None
        if data.get('uploadDate'):
            try:
                upload_date = datetime.fromisoformat(data['uploadDate'].replace('Z', '+00:00'))
            except Exception:
                pass
        
        # 确定作品类型
        illust_type = data.get('illustType', 0)
        if illust_type == 0:
            artwork_type = ArtworkType.ILLUSTRATION
        elif illust_type == 1:
            artwork_type = ArtworkType.MANGA
        elif illust_type == 2:
            artwork_type = ArtworkType.UGOIRA
        else:
            artwork_type = ArtworkType.ILLUSTRATION
        
        # 创建作品对象
        artwork = Artwork(
            id=int(data['id']),
            title=data.get('title', 'Unknown'),
            author_id=int(data.get('userId', 0)),
            author_name=data.get('userName', 'Unknown'),
            type=artwork_type,
            tags=[tag['tag'] for tag in data.get('tags', {}).get('tags', [])],
            description=data.get('description', ''),
            view_count=data.get('viewCount', 0),
            like_count=data.get('likeCount', 0),
            bookmark_count=data.get('bookmarkCount', 0),
            upload_date=upload_date
        )
        
        return artwork
    
    def _create_artwork_from_ranking_data(self, data: Dict[str, Any]) -> Optional[Artwork]:
        """从排行榜数据创建作品对象"""
        try:
            artwork_id = int(data['illust_id'])
            detail_data = self.api_service.get_artwork_detail(artwork_id)
            
            if detail_data and 'body' in detail_data:
                return self._create_artwork_from_detail_data(detail_data['body'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"从排行榜数据创建作品对象失败: {e}")
            return None
    
    def _create_artwork_from_search_data(self, data: Dict[str, Any]) -> Optional[Artwork]:
        """从搜索数据创建作品对象"""
        try:
            artwork_id = int(data['id'])
            detail_data = self.api_service.get_artwork_detail(artwork_id)
            
            if detail_data and 'body' in detail_data:
                return self._create_artwork_from_detail_data(detail_data['body'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"从搜索数据创建作品对象失败: {e}")
            return None
    
    def _should_filter_artwork(self, artwork: Artwork) -> bool:
        """检查是否应该过滤作品"""
        # 收藏数过滤
        if self.download_config.min_bookmarks > 0 and artwork.bookmark_count < self.download_config.min_bookmarks:
            return True
        
        if self.download_config.max_bookmarks > 0 and artwork.bookmark_count > self.download_config.max_bookmarks:
            return True
        
        # 页数过滤
        if self.download_config.min_pages > 0 and artwork.page_count < self.download_config.min_pages:
            return True
        
        if self.download_config.max_pages > 0 and artwork.page_count > self.download_config.max_pages:
            return True
        
        # 标签过滤
        if self.download_config.exclude_tags:
            for exclude_tag in self.download_config.exclude_tags:
                if any(exclude_tag.lower() in tag.lower() for tag in artwork.tags):
                    return True
        
        if self.download_config.include_tags:
            has_include_tag = False
            for include_tag in self.download_config.include_tags:
                if any(include_tag.lower() in tag.lower() for tag in artwork.tags):
                    has_include_tag = True
                    break
            if not has_include_tag:
                return True
        
        return False
    
    def stop_download(self) -> None:
        """停止下载并立即清理资源"""
        if self._is_running:
            self._is_running = False
            # 通知下载服务停止
            if self.download_service:
                self.download_service.stop_download()
            self._notify_status("正在停止下载...")
            self.logger.info("下载已停止")
            
            # 立即清理资源
            self.cleanup_resources()
    
    def cleanup_resources(self) -> None:
        """清理所有资源"""
        try:
            self.logger.info("开始清理爬虫资源...")
            
            # 清理下载服务
            if self.download_service:
                self.download_service.cleanup_resources()
            
            # 清理Selenium驱动器
            if self.selenium_driver:
                self.selenium_driver.quit()
                self.selenium_driver = None
                self.logger.info("Selenium驱动器已清理")
            
            # 清理API服务会话池
            if self.api_service:
                # API服务有自己的清理机制
                del self.api_service
                self.api_service = None
                self.logger.info("API服务已清理")
            
            # 重置状态
            self._is_running = False
            
            self.logger.info("爬虫资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理爬虫资源时出错: {e}")
    
    def reset_for_new_download(self) -> None:
        """为新的下载任务重置状态"""
        try:
            self.logger.info("重置爬虫状态...")
            
            # 确保不在运行状态
            self._is_running = False
            
            # 重新初始化服务（如果需要）
            if not self.api_service and self._cookies:
                self.api_service = self.registry.create_configured_api_service(self._cookies)

            if not self.download_service and self.api_service:
                self.download_service = self.registry.create_configured_download_service(self.api_service)
                # 设置回调
                self.download_service.set_progress_callback(self._progress_callback)
                self.download_service.set_status_callback(self._status_callback)
            elif self.download_service:
                # 重置下载服务状态
                self.download_service.reset_state()
            
            self.logger.info("爬虫状态重置完成")
            
        except Exception as e:
            self.logger.error(f"重置爬虫状态时出错: {e}")
    
    def get_download_stats(self) -> dict:
        """获取下载统计信息"""
        return {
            "is_running": self._is_running,
            "is_authenticated": self._is_authenticated,
            "download_mode": self.download_config.download_mode.value,
            "save_path": self.download_config.save_path
        }
    
    def update_download_config(self, config: DownloadConfig) -> None:
        """更新下载配置"""
        self.download_config = config
        self.config_manager.save_download_config(config)
        
        # 更新下载服务的配置
        if self.download_service:
            self.download_service.download_config = config
        
        self.logger.info("下载配置已更新")
    
    def update_spider_config(self, config: SpiderConfig) -> None:
        """更新爬虫配置"""
        self.spider_config = config
        self.config_manager.save_spider_config(config)
        self.logger.info("爬虫配置已更新")
    
    def _notify_progress(self, current: int, total: int, message: str = "") -> None:
        """通知进度更新"""
        if self._progress_callback:
            try:
                self._progress_callback(current, total, message)
            except Exception as e:
                self.logger.error(f"进度回调函数执行失败: {e}")
    
    def _notify_status(self, message: str) -> None:
        """通知状态更新"""
        if self._status_callback:
            try:
                self._status_callback(message)
            except Exception as e:
                self.logger.error(f"状态回调函数执行失败: {e}")
    
    def __enter__(self):
        """支持上下文管理器"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持上下文管理器"""
        if self._is_running:
            self.stop_download()

        # 清理资源
        if self.selenium_driver:
            self.selenium_driver.quit()

        # 不抑制异常
        return False