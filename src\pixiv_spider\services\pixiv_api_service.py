"""
Pixiv API服务

负责与Pixiv API的交互、会话管理和缓存
"""

import queue
import requests
import logging
import concurrent.futures
from functools import lru_cache
from typing import Optional, Dict, Any, List, Union
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..interfaces.api_interface import IApiService
from ..models.exceptions import NetworkError, AuthenticationError
from ..config.config_manager import ConfigManager
from ..config.settings import PIXIV_BASE_URL, PIXIV_API_BASE_URL, DEFAULT_USER_AGENT, DEFAULT_REFERER


class PixivApiService(IApiService):
    """Pixiv API服务类"""
    
    def __init__(self, cookies: List[Dict[str, Any]], config_manager: Optional[ConfigManager] = None):
        """
        初始化API服务
        
        Args:
            cookies: 认证cookies
            config_manager: 配置管理器
        """
        self.cookies = cookies
        self.config_manager = config_manager or ConfigManager()
        self.spider_config = self.config_manager.load_spider_config()
        self.logger = logging.getLogger(__name__)
        
        # 会话池
        self.session_pool = queue.Queue()
        self.max_workers = self.spider_config.max_workers
        
        # 请求头
        self.headers = {
            'Referer': self.spider_config.referer,
            'User-Agent': self.spider_config.user_agent,
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache'
        }
        
        # 初始化会话池
        self._initialize_session_pool()
        
        # API缓存
        self._setup_cache()
    
    def _initialize_session_pool(self) -> None:
        """初始化会话池"""
        for _ in range(self.max_workers):
            session = self._create_session()
            self.session_pool.put(session)
        
        self.logger.info(f"初始化会话池，大小: {self.max_workers}")
    
    def _create_session(self) -> requests.Session:
        """创建单个会话"""
        session = requests.Session()
        
        # 设置cookies
        for cookie in self.cookies:
            session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
        
        # 设置请求头
        session.headers.update(self.headers)
        
        # 设置重试策略
        retry_strategy = Retry(
            total=self.spider_config.retry_attempts,
            backoff_factor=self.spider_config.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置代理
        if self.spider_config.proxy:
            session.proxies = {
                'http': self.spider_config.proxy,
                'https': self.spider_config.proxy
            }
        
        return session
    
    def _setup_cache(self) -> None:
        """设置缓存"""
        # 使用lru_cache装饰器的缓存大小
        self.cached_api_call = lru_cache(maxsize=self.spider_config.api_cache_size)(self._api_call)
    
    def get_session(self) -> requests.Session:
        """获取会话"""
        try:
            return self.session_pool.get_nowait()
        except queue.Empty:
            # 如果池为空，创建新会话
            return self._create_session()
    
    def return_session(self, session: requests.Session) -> None:
        """归还会话"""
        try:
            self.session_pool.put_nowait(session)
        except queue.Full:
            # 如果池已满，关闭会话
            session.close()
    
    def _handle_response(self, response: requests.Response, url: str) -> Optional[Dict[str, Any]]:
        """统一的响应处理逻辑"""
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 401:
            raise AuthenticationError("API认证失败，请检查登录状态")
        elif response.status_code == 429:
            raise NetworkError("请求频率过高，请稍后再试")
        else:
            self.logger.warning(f"API请求返回状态码: {response.status_code}, URL: {url}")
            return None

    def _handle_request_exceptions(self, e: Exception, url: str) -> None:
        """统一的请求异常处理"""
        if isinstance(e, requests.exceptions.Timeout):
            raise NetworkError(f"请求超时: {url}")
        elif isinstance(e, requests.exceptions.ConnectionError):
            raise NetworkError(f"连接错误: {url}")
        else:
            self.logger.error(f"请求失败: {url}, 错误: {e}")
            raise NetworkError(f"请求失败: {e}")

    def _api_call(self, url: str) -> Optional[Dict[str, Any]]:
        """内部API调用方法（用于缓存）"""
        response = self.make_request(url)
        if response:
            return self._handle_response(response, url)
        return None
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """
        通用请求方法

        Args:
            url: 请求URL
            method: 请求方法
            **kwargs: 其他请求参数

        Returns:
            requests.Response: 响应对象
        """
        session = self.get_session()
        try:
            # 设置默认超时
            kwargs.setdefault('timeout', self.spider_config.request_timeout)

            # 执行请求
            response = session.request(method.upper(), url, **kwargs)
            return response

        except Exception as e:
            self._handle_request_exceptions(e, url)
        finally:
            self.return_session(session)
    
    def get_artwork_detail(self, artwork_id: int) -> Optional[Dict[str, Any]]:
        """
        获取作品详情
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Dict: 作品详情数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}"
        return self.cached_api_call(url)

    def get_artwork_details_batch(self, artwork_ids: List[int], max_workers: int = None) -> Dict[int, Optional[Dict[str, Any]]]:
        """
        批量获取作品详情

        Args:
            artwork_ids: 作品ID列表
            max_workers: 最大并发数

        Returns:
            Dict[int, Optional[Dict]]: 作品ID到详情数据的映射
        """
        if not artwork_ids:
            return {}

        if max_workers is None:
            max_workers = min(self.max_workers, len(artwork_ids))

        results = {}

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_id = {
                executor.submit(self.get_artwork_detail, artwork_id): artwork_id
                for artwork_id in artwork_ids
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_id):
                artwork_id = future_to_id[future]
                try:
                    result = future.result(timeout=30)
                    results[artwork_id] = result
                except Exception as e:
                    self.logger.error(f"批量获取作品详情失败: {artwork_id}, 错误: {e}")
                    results[artwork_id] = None

        return results
    
    def get_artwork_pages(self, artwork_id: int) -> Optional[Dict[str, Any]]:
        """
        获取作品页面信息
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Dict: 页面信息数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}/pages"
        return self.cached_api_call(url)
    
    def get_user_artworks(self, user_id: int, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取用户作品列表
        
        Args:
            user_id: 用户ID
            page: 页码
            
        Returns:
            Dict: 用户作品数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/user/{user_id}/profile/all"
        if page > 1:
            url += f"?page={page}"
        return self.cached_api_call(url)
    
    def get_ranking_artworks(self, mode: str = "daily", date: str = None, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取排行榜作品
        
        Args:
            mode: 排行榜模式 (daily, weekly, monthly等)
            date: 日期 (YYYY-MM-DD格式)
            page: 页码
            
        Returns:
            Dict: 排行榜数据
        """
        url = f"{PIXIV_BASE_URL}/ranking.php"
        params = {
            'mode': mode,
            'format': 'json',
            'page': page
        }
        if date:
            params['date'] = date
        
        # 对于排行榜，不使用缓存
        session = self.get_session()
        try:
            response = session.get(url, params=params, timeout=self.spider_config.request_timeout)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.logger.error(f"获取排行榜失败: {e}")
            return None
        finally:
            self.return_session(session)
    
    def search_artworks(self, keyword: str, order: str = "date_desc", mode: str = "all", page: int = 1) -> Optional[Dict[str, Any]]:
        """
        搜索作品
        
        Args:
            keyword: 搜索关键词
            order: 排序方式
            mode: 搜索模式
            page: 页码
            
        Returns:
            Dict: 搜索结果数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/search/artworks/{keyword}"
        params = {
            'word': keyword,
            'order': order,
            'mode': mode,
            'p': page,
            's_mode': 's_tag'
        }
        
        session = self.get_session()
        try:
            response = session.get(url, params=params, timeout=self.spider_config.request_timeout)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.logger.error(f"搜索作品失败: {e}")
            return None
        finally:
            self.return_session(session)
    
    def download_file(self, url: str, save_path: str) -> bool:
        """
        下载文件
        
        Args:
            url: 文件URL
            save_path: 保存路径
            
        Returns:
            bool: 是否下载成功
        """
        session = self.get_session()
        try:
            response = session.get(
                url, 
                timeout=self.spider_config.request_timeout,
                stream=True
            )
            
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                return True
            else:
                self.logger.warning(f"下载文件失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"下载文件失败: {url}, 错误: {e}")
            return False
        finally:
            self.return_session(session)
    
    def clear_cache(self) -> None:
        """清空API缓存"""
        self.cached_api_call.cache_clear()
        self.logger.info("API缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        cache_info = self.cached_api_call.cache_info()
        return {
            'hits': cache_info.hits,
            'misses': cache_info.misses,
            'maxsize': cache_info.maxsize,
            'currsize': cache_info.currsize,
            'hit_rate': cache_info.hits / (cache_info.hits + cache_info.misses) if (cache_info.hits + cache_info.misses) > 0 else 0
        }
    
    def __del__(self):
        """析构函数，清理会话池"""
        while not self.session_pool.empty():
            try:
                session = self.session_pool.get_nowait()
                session.close()
            except queue.Empty:
                break 