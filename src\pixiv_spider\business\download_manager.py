"""
下载管理器

负责管理下载流程和状态
"""

import logging
from typing import List, Dict, Any, Optional, Callable
from ..models.artwork import Artwork
from ..interfaces.download_interface import IDownloadService


class DownloadManager:
    """下载管理器"""
    
    def __init__(self, download_service: IDownloadService):
        """
        初始化下载管理器
        
        Args:
            download_service: 下载服务接口
        """
        self.download_service = download_service
        self.logger = logging.getLogger(__name__)
        self._download_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def start_batch_download(self, artworks: List[Artwork]) -> Dict[str, int]:
        """
        开始批量下载
        
        Args:
            artworks: 作品列表
            
        Returns:
            Dict[str, int]: 下载统计信息
        """
        if not artworks:
            return self._download_stats
        
        self.logger.info(f"开始批量下载 {len(artworks)} 个作品")
        
        # 重置统计信息
        self._download_stats = {
            'total': len(artworks),
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 执行批量下载
        stats = self.download_service.batch_download(artworks)
        
        # 更新统计信息
        self._download_stats.update(stats)
        
        self.logger.info(f"批量下载完成: {stats}")
        return self._download_stats
    
    def get_download_stats(self) -> Dict[str, int]:
        """获取下载统计信息"""
        return self._download_stats.copy()
    
    def set_progress_callback(self, callback: Optional[Callable]) -> None:
        """设置进度回调函数"""
        self.download_service.set_progress_callback(callback)
    
    def set_status_callback(self, callback: Optional[Callable]) -> None:
        """设置状态回调函数"""
        self.download_service.set_status_callback(callback)
    
    def stop_download(self) -> None:
        """停止下载"""
        self.download_service.stop_download()
        self.logger.info("下载已停止")
