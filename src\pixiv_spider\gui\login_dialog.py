#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录对话框模块
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Optional, Tuple, Dict, Any


class LoginDialog:
    """登录对话框"""
    
    def __init__(self, parent, auth_service):
        """
        初始化登录对话框

        Args:
            parent: 父窗口
            auth_service: 认证服务
        """
        self.parent = parent
        self.auth_service = auth_service
        self.result = None
        self.cookies = None
        self.selenium_driver = None  # Selenium驱动实例

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Pixiv 登录")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_window()

        # 创建界面
        self._create_widgets()

        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
    
    def _center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        
        # 获取窗口尺寸
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        
        # 获取屏幕尺寸
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="Pixiv 登录", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 说明文本
        info_text = tk.Text(
            main_frame, 
            height=12, 
            wrap=tk.WORD, 
            state=tk.DISABLED,
            font=("Arial", 10)
        )
        info_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 添加说明内容
        info_content = """🔐 Pixiv 登录说明

请按照以下步骤完成登录：

1. 点击下方的"打开登录页面"按钮
2. 在打开的浏览器中登录你的Pixiv账号
3. 登录成功后，确保能正常访问Pixiv主页
4. 回到这个对话框，点击"确认登录完成"按钮

💡 提示：
• 如果浏览器没有自动打开，请手动访问：https://www.pixiv.net/
• 登录时可能需要验证码或邮箱确认
• 请保持浏览器打开状态直到登录完成
"""
        
        info_text.config(state=tk.NORMAL)
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # 打开登录页面按钮
        self.open_btn = ttk.Button(
            button_frame,
            text="打开登录页面",
            command=self._open_login_page
        )
        self.open_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 确认登录完成按钮
        self.confirm_btn = ttk.Button(
            button_frame,
            text="确认登录完成",
            command=self._confirm_login,
            state=tk.DISABLED
        )
        self.confirm_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        ttk.Button(
            button_frame,
            text="取消",
            command=self._on_cancel
        ).pack(side=tk.RIGHT)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="", foreground="blue")
        self.status_label.pack(pady=(10, 0))
    
    def _open_login_page(self):
        """打开登录页面（使用Selenium控制的浏览器）"""
        try:
            self.status_label.config(text="正在启动浏览器...", foreground="blue")
            self.open_btn.config(state=tk.DISABLED)
            self.dialog.update()

            # 在后台线程中启动Selenium浏览器
            def start_browser():
                try:
                    from selenium import webdriver
                    from selenium.webdriver.chrome.options import Options

                    # 设置Chrome选项
                    options = Options()
                    options.add_argument('--no-sandbox')
                    options.add_argument('--disable-dev-shm-usage')
                    options.add_argument('--disable-gpu')
                    options.add_argument('--window-size=1200,800')

                    # 启动浏览器
                    self.selenium_driver = webdriver.Chrome(options=options)
                    self.selenium_driver.get('https://www.pixiv.net/')

                    # 在主线程中更新UI
                    self.dialog.after(0, self._browser_opened_successfully)

                except Exception as e:
                    # 在主线程中显示错误
                    self.dialog.after(0, lambda: self._browser_open_failed(e))

            threading.Thread(target=start_browser, daemon=True).start()

        except Exception as e:
            self.status_label.config(text=f"❌ 启动浏览器失败: {e}", foreground="red")
            self.open_btn.config(state=tk.NORMAL)

    def _browser_opened_successfully(self):
        """浏览器成功打开的回调"""
        self.status_label.config(text="✅ 浏览器已打开，请在浏览器中完成登录", foreground="green")
        self.confirm_btn.config(state=tk.NORMAL)

    def _browser_open_failed(self, error):
        """浏览器打开失败的回调"""
        self.status_label.config(text=f"❌ 启动浏览器失败: {error}", foreground="red")
        self.open_btn.config(state=tk.NORMAL)
    
    def _confirm_login(self):
        """确认登录完成"""
        try:
            if not self.selenium_driver:
                self.status_label.config(text="❌ 请先打开登录页面", foreground="red")
                return

            self.status_label.config(text="🔄 正在验证登录状态...", foreground="blue")
            self.confirm_btn.config(state=tk.DISABLED)
            self.dialog.update()

            # 在后台线程中获取cookies
            def get_cookies():
                try:
                    # 使用已经打开的浏览器获取cookies
                    success, cookies = self._get_cookies_from_current_browser()

                    # 在主线程中更新UI
                    self.dialog.after(0, lambda: self._handle_login_result(success, cookies))

                except Exception as e:
                    self.dialog.after(0, lambda: self._handle_login_error(e))

            threading.Thread(target=get_cookies, daemon=True).start()

        except Exception as e:
            self._handle_login_error(e)

    def _get_cookies_from_current_browser(self):
        """从当前浏览器获取cookies"""
        try:
            # 等待页面加载
            time.sleep(2)

            # 检查登录状态
            if self._check_login_in_current_browser():
                # 获取cookies
                cookies = self.selenium_driver.get_cookies()

                # 保存cookies
                self.auth_service.config_manager.save_cookies(cookies)
                return True, cookies
            else:
                return False, None

        except Exception as e:
            raise e

    def _check_login_in_current_browser(self):
        """检查当前浏览器的登录状态"""
        try:
            from selenium.webdriver.common.by import By

            # 检查是否存在登录相关元素
            try:
                # 查找用户头像或导航栏中的用户相关元素
                user_elements = self.selenium_driver.find_elements(By.CSS_SELECTOR,
                    'a[data-gtm-value="header_user_menu"], '
                    'img[alt*="avatar"], '
                    '.user-icon, '
                    '[data-testid="header-user-menu"]'
                )

                if user_elements:
                    return True

                # 检查是否存在登录按钮（如果存在说明未登录）
                login_buttons = self.selenium_driver.find_elements(By.CSS_SELECTOR,
                    'a[href*="login"], '
                    'button[data-gtm-value="header_login"]'
                )

                if login_buttons:
                    return False

                # 如果都没找到，尝试检查URL
                current_url = self.selenium_driver.current_url
                if 'login' in current_url:
                    return False

                # 默认假设已登录
                return True

            except Exception:
                # 出错时假设已登录
                return True

        except Exception:
            return False
    
    def _handle_login_result(self, success: bool, cookies: Optional[Dict[str, Any]]):
        """处理登录结果"""
        if success and cookies:
            self.status_label.config(text=f"✅ 登录成功！获得 {len(cookies)} 个cookie", foreground="green")
            self.result = True
            self.cookies = cookies
            
            # 延迟关闭对话框
            self.dialog.after(1500, self._close_dialog)
        else:
            self.status_label.config(text="❌ 检测到未登录状态，请重新登录", foreground="red")
            self.confirm_btn.config(state=tk.NORMAL)
    
    def _handle_login_error(self, error: Exception):
        """处理登录错误"""
        self.status_label.config(text=f"❌ 登录验证失败: {error}", foreground="red")
        self.confirm_btn.config(state=tk.NORMAL)
    
    def _on_cancel(self):
        """取消登录"""
        self.result = False
        self._close_dialog()
    
    def _close_dialog(self):
        """关闭对话框"""
        # 清理Selenium驱动
        if self.selenium_driver:
            try:
                self.selenium_driver.quit()
            except Exception:
                pass
            self.selenium_driver = None

        self.dialog.grab_release()
        self.dialog.destroy()
    
    def show(self) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        显示对话框并等待结果
        
        Returns:
            Tuple[bool, Optional[Dict]]: (是否成功, Cookie数据)
        """
        # 等待对话框关闭
        self.dialog.wait_window()
        
        return self.result or False, self.cookies
