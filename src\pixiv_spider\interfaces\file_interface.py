"""
文件服务接口
"""

from abc import ABC, abstractmethod
from typing import Optional, List


class IFileService(ABC):
    """文件服务接口"""
    
    @abstractmethod
    def ensure_directory(self, path: str) -> bool:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    def safe_filename(self, filename: str, max_length: int = 255) -> str:
        """
        生成安全的文件名
        
        Args:
            filename: 原始文件名
            max_length: 最大长度
            
        Returns:
            str: 安全的文件名
        """
        pass
    
    @abstractmethod
    def get_file_size(self, file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小（字节）
        """
        pass
    
    @abstractmethod
    def copy_file(self, src: str, dst: str) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            bool: 是否复制成功
        """
        pass
    
    @abstractmethod
    def delete_file(self, file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否删除成功
        """
        pass
    
    @abstractmethod
    def list_files(self, directory: str, pattern: Optional[str] = None) -> List[str]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式（可选）
            
        Returns:
            List[str]: 文件路径列表
        """
        pass
