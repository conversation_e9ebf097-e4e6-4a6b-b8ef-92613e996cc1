"""
Pixiv Spider 现代化主窗口GUI (CustomTkinter版本)

使用CustomTkinter重构的现代化图形用户界面
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog
import threading
import os
import time
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from ..core.pixiv_spider import PixivSpider
from ..config.config_manager import ConfigManager
from ..models.config import (
    DownloadConfig, SpiderConfig, DownloadMode, ClassifyMode, GifMode,
    RankingConfig, RankingCategory, RankingRating, RankingPeriod,
    SearchCategory, SearchBookmarkCount, SearchContentMode
)
from ..models.exceptions import PixivSpiderError, AuthenticationError


class ModernPixivSpiderGUI:
    """Pixiv Spider 现代化图形用户界面 (CustomTkinter版本)"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化现代化GUI应用
        
        Args:
            config_manager: 配置管理器
        """
        # 设置CustomTkinter主题和性能优化
        ctk.set_appearance_mode("dark")  # 默认暗色主题
        ctk.set_default_color_theme("blue")  # 蓝色主题

        # 性能优化设置
        try:
            ctk.deactivate_automatic_dpi_awareness()  # 禁用自动DPI感知以提高性能
        except:
            pass  # 如果方法不存在则忽略

        self.config_manager = config_manager or ConfigManager()
        self.spider = PixivSpider(self.config_manager)
        
        # 加载配置
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 状态变量
        self.is_running = False
        self.is_authenticated = False
        self.download_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'total': 0,
            'completed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 创建主窗口
        self.root = ctk.CTk()

        # 设置渲染优化
        self._setup_rendering_optimizations()

        self.setup_ui()
        self.setup_callbacks()
        self.load_settings()
        
        # 性能优化变量
        self.last_progress_update = 0
        self.progress_update_interval = 0.1  # 限制进度更新频率（秒）

        # 初始化检查登录状态
        self.root.after(100, self.check_login_status)  # 延迟执行以提高启动速度

    def _setup_rendering_optimizations(self):
        """设置渲染优化"""
        # 禁用不必要的动画和特效
        self.root.configure(highlightthickness=0)

        # 设置更新频率限制
        self.last_ui_update = 0
        self.ui_update_interval = 0.05  # 20 FPS

        # 绑定窗口事件以优化渲染
        self.root.bind('<Configure>', self._on_window_configure)
        self.root.bind('<Map>', self._on_window_map)
        self.root.bind('<Unmap>', self._on_window_unmap)

        # 设置渲染状态
        self.is_window_mapped = True
        self.resize_timer = None

    def _on_window_configure(self, event):
        """窗口配置改变时的处理"""
        if event.widget == self.root:
            # 延迟处理窗口大小改变，避免频繁重绘
            if self.resize_timer:
                self.root.after_cancel(self.resize_timer)
            self.resize_timer = self.root.after(50, self._handle_resize)

    def _handle_resize(self):
        """处理窗口大小改变"""
        self.resize_timer = None
        # 强制更新布局
        self.root.update_idletasks()

    def _on_window_map(self, event):
        """窗口映射时的处理"""
        if event.widget == self.root:
            self.is_window_mapped = True

    def _on_window_unmap(self, event):
        """窗口取消映射时的处理"""
        if event.widget == self.root:
            self.is_window_mapped = False
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("Pixiv Spider - 现代化版本 v4.0.0")
        self.root.geometry("1200x900")
        self.root.minsize(1000, 700)

        # 配置网格权重
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # 创建主滚动框架
        self.main_scrollable_frame = ctk.CTkScrollableFrame(
            self.root,
            label_text="Pixiv Spider - 现代化控制面板",
            label_font=ctk.CTkFont(size=20, weight="bold")
        )
        self.main_scrollable_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        self.main_scrollable_frame.grid_columnconfigure(0, weight=1)

        # 设置各个UI部分
        self._setup_theme_frame()
        self._setup_login_frame()
        self._setup_download_settings_frame()
        self._setup_performance_frame()
        self._setup_control_frame()
        self._setup_progress_frame()
        self._setup_stats_frame()
        self._setup_log_frame()

    def _setup_theme_frame(self):
        """设置简化的高性能布局"""
        # 创建主要的notebook标签页布局
        self.notebook = ctk.CTkTabview(self.root)
        self.notebook.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        # 创建标签页
        self.settings_tab = self.notebook.add("设置")
        self.status_tab = self.notebook.add("状态")
        self.log_tab = self.notebook.add("日志")

        # 设置各个标签页
        self._setup_settings_tab()
        self._setup_status_tab()
        self._setup_log_tab()

        # 默认选择设置标签页
        self.notebook.set("设置")

    def _setup_settings_tab(self):
        """设置配置标签页"""
        # 配置网格权重
        self.settings_tab.grid_columnconfigure(0, weight=1)
        self.settings_tab.grid_rowconfigure(0, weight=1)

        # 创建滚动框架，但使用更轻量的配置
        settings_scroll = ctk.CTkScrollableFrame(
            self.settings_tab,
            scrollbar_button_color="gray30",
            scrollbar_button_hover_color="gray20"
        )
        settings_scroll.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        settings_scroll.grid_columnconfigure(0, weight=1)

        # 在滚动框架中添加设置组件
        self._add_settings_components(settings_scroll)

    def _setup_status_tab(self):
        """设置状态标签页"""
        self.status_tab.grid_columnconfigure(0, weight=1)
        self.status_tab.grid_rowconfigure(2, weight=1)

        # 直接在标签页中添加状态组件
        self._add_status_components(self.status_tab)

    def _setup_log_tab(self):
        """设置日志标签页"""
        self.log_tab.grid_columnconfigure(0, weight=1)
        self.log_tab.grid_rowconfigure(0, weight=1)

        # 直接在标签页中添加日志组件
        self._add_log_components(self.log_tab)

    def _add_settings_components(self, parent):
        """添加设置组件到父容器"""
        # 主题设置
        self._setup_theme_frame_simple(parent)
        # 登录状态
        self._setup_login_frame_simple(parent)
        # 下载设置
        self._setup_download_settings_frame_simple(parent)
        # 性能设置
        self._setup_performance_frame_simple(parent)
        # 控制按钮
        self._setup_control_frame_simple(parent)

    def _add_status_components(self, parent):
        """添加状态组件到父容器"""
        # 进度显示
        self._setup_progress_frame_simple(parent)
        # 统计信息
        self._setup_stats_frame_simple(parent)

    def _add_log_components(self, parent):
        """添加日志组件到父容器"""
        # 日志显示
        self._setup_log_frame_simple(parent)

    def _setup_theme_frame_simple(self, parent):
        """简化版主题设置框架"""
        theme_frame = ctk.CTkFrame(parent)
        theme_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        theme_frame.grid_columnconfigure(1, weight=1)

        # 主题标签
        theme_label = ctk.CTkLabel(theme_frame, text="界面主题:", font=ctk.CTkFont(size=14, weight="bold"))
        theme_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")

        # 主题切换
        self.theme_var = tk.StringVar(value="dark")
        theme_switch = ctk.CTkSegmentedButton(
            theme_frame,
            values=["light", "dark", "system"],
            variable=self.theme_var,
            command=self._change_theme
        )
        theme_switch.grid(row=0, column=1, padx=10, pady=10, sticky="w")
        theme_switch.set("dark")

    def _setup_login_frame_simple(self, parent):
        """简化版登录状态框架"""
        login_frame = ctk.CTkFrame(parent)
        login_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        login_frame.grid_columnconfigure(2, weight=1)

        # 标题
        login_title = ctk.CTkLabel(login_frame, text="🔐 登录状态", font=ctk.CTkFont(size=16, weight="bold"))
        login_title.grid(row=0, column=0, columnspan=3, padx=10, pady=(10, 5), sticky="w")

        # 状态显示
        self.login_status_label = ctk.CTkLabel(login_frame, text="检查中...", font=ctk.CTkFont(size=14), text_color="orange")
        self.login_status_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        # 按钮
        check_btn = ctk.CTkButton(login_frame, text="检查登录", command=self.check_login_status, width=100)
        check_btn.grid(row=1, column=1, padx=5, pady=5)

        relogin_btn = ctk.CTkButton(login_frame, text="重新登录", command=self.start_login, width=100)
        relogin_btn.grid(row=1, column=2, padx=5, pady=5)

    def _setup_download_settings_frame_simple(self, parent):
        """简化版下载设置框架"""
        settings_frame = ctk.CTkFrame(parent)
        settings_frame.grid(row=2, column=0, padx=5, pady=5, sticky="ew")
        settings_frame.grid_columnconfigure(1, weight=1)

        # 标题
        settings_title = ctk.CTkLabel(settings_frame, text="📥 下载设置", font=ctk.CTkFont(size=16, weight="bold"))
        settings_title.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")

        # 下载模式
        mode_label = ctk.CTkLabel(settings_frame, text="下载模式:")
        mode_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        self.download_mode_var = tk.StringVar(value=self.download_config.download_mode.value)
        mode_menu = ctk.CTkOptionMenu(
            settings_frame,
            values=["date", "ranking", "search", "user"],
            variable=self.download_mode_var,
            command=self._on_download_mode_changed
        )
        mode_menu.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        # 其他基本设置（简化版）
        self._add_basic_download_settings(settings_frame)

    def _add_basic_download_settings(self, parent):
        """添加基本下载设置"""
        row = 2

        # 搜索关键词
        self.search_keyword_label = ctk.CTkLabel(parent, text="搜索关键词:")
        self.search_keyword_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        self.search_keyword_var = tk.StringVar(value=self.download_config.search_keyword)
        self.search_keyword_entry = ctk.CTkEntry(parent, textvariable=self.search_keyword_var)
        self.search_keyword_entry.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        row += 1

        # 用户ID
        self.user_id_label = ctk.CTkLabel(parent, text="用户ID:")
        self.user_id_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        self.user_id_var = tk.StringVar(value=str(self.download_config.user_id) if self.download_config.user_id else "")
        self.user_id_entry = ctk.CTkEntry(parent, textvariable=self.user_id_var)
        self.user_id_entry.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        row += 1

        # 页码范围
        page_label = ctk.CTkLabel(parent, text="页码范围:")
        page_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        page_frame = ctk.CTkFrame(parent, fg_color="transparent")
        page_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        self.start_page_var = tk.StringVar(value=str(self.download_config.start_page))
        self.end_page_var = tk.StringVar(value=str(self.download_config.end_page))

        self.start_page_entry = ctk.CTkEntry(page_frame, textvariable=self.start_page_var, width=80)
        self.start_page_entry.pack(side="left", padx=2)

        ctk.CTkLabel(page_frame, text="到").pack(side="left", padx=5)

        self.end_page_entry = ctk.CTkEntry(page_frame, textvariable=self.end_page_var, width=80)
        self.end_page_entry.pack(side="left", padx=2)

        self.page_note_label = ctk.CTkLabel(page_frame, text="页")
        self.page_note_label.pack(side="left", padx=5)

    def _setup_performance_frame_simple(self, parent):
        """简化版性能设置框架"""
        perf_frame = ctk.CTkFrame(parent)
        perf_frame.grid(row=3, column=0, padx=5, pady=5, sticky="ew")
        perf_frame.grid_columnconfigure(1, weight=1)

        # 标题
        perf_title = ctk.CTkLabel(perf_frame, text="⚡ 性能设置", font=ctk.CTkFont(size=16, weight="bold"))
        perf_title.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")

        # 并发数
        concurrent_label = ctk.CTkLabel(perf_frame, text="并发数:")
        concurrent_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.concurrent_var = tk.StringVar(value=str(self.spider_config.concurrent_downloads))
        concurrent_entry = ctk.CTkEntry(perf_frame, textvariable=self.concurrent_var, width=100)
        concurrent_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # 性能模式
        perf_mode_label = ctk.CTkLabel(perf_frame, text="性能模式:")
        perf_mode_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        self.performance_mode_var = tk.StringVar(value="balanced")
        perf_mode_menu = ctk.CTkOptionMenu(
            perf_frame,
            values=["high_performance", "balanced", "low_resource"],
            variable=self.performance_mode_var,
            command=self._on_performance_mode_changed
        )
        perf_mode_menu.grid(row=2, column=1, padx=10, pady=5, sticky="w")

    def _setup_control_frame_simple(self, parent):
        """简化版控制按钮框架"""
        control_frame = ctk.CTkFrame(parent)
        control_frame.grid(row=4, column=0, padx=5, pady=5, sticky="ew")

        # 标题
        control_title = ctk.CTkLabel(control_frame, text="🎮 控制面板", font=ctk.CTkFont(size=16, weight="bold"))
        control_title.grid(row=0, column=0, columnspan=4, padx=10, pady=(10, 5), sticky="w")

        # 按钮容器
        button_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        button_frame.grid(row=1, column=0, columnspan=4, padx=10, pady=10, sticky="ew")

        # 开始下载按钮
        self.start_btn = ctk.CTkButton(button_frame, text="🚀 开始下载", command=self.start_download, width=120, height=40)
        self.start_btn.pack(side="left", padx=5)

        # 停止下载按钮
        self.stop_btn = ctk.CTkButton(button_frame, text="⏹️ 停止下载", command=self.stop_download, width=120, height=40, state="disabled")
        self.stop_btn.pack(side="left", padx=5)

        # 保存设置按钮
        save_btn = ctk.CTkButton(button_frame, text="💾 保存设置", command=self.save_settings, width=120, height=40)
        save_btn.pack(side="left", padx=5)

        # 清空日志按钮
        clear_btn = ctk.CTkButton(button_frame, text="🗑️ 清空日志", command=self.clear_log, width=120, height=40)
        clear_btn.pack(side="left", padx=5)

    def _setup_progress_frame_simple(self, parent):
        """简化版进度显示框架"""
        progress_frame = ctk.CTkFrame(parent)
        progress_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        progress_frame.grid_columnconfigure(1, weight=1)

        # 标题
        progress_title = ctk.CTkLabel(progress_frame, text="📊 下载进度", font=ctk.CTkFont(size=16, weight="bold"))
        progress_title.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")

        # 当前状态
        status_label = ctk.CTkLabel(progress_frame, text="当前状态:")
        status_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.status_label = ctk.CTkLabel(progress_frame, text="等待开始...", font=ctk.CTkFont(size=12))
        self.status_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # 进度条
        progress_bar_label = ctk.CTkLabel(progress_frame, text="总体进度:")
        progress_bar_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        self.progress_bar = ctk.CTkProgressBar(progress_frame)
        self.progress_bar.grid(row=2, column=1, padx=10, pady=5, sticky="ew")
        self.progress_bar.set(0)

        # 进度百分比
        self.progress_label = ctk.CTkLabel(progress_frame, text="0%")
        self.progress_label.grid(row=3, column=1, padx=10, pady=5, sticky="w")

    def _setup_stats_frame_simple(self, parent):
        """简化版统计信息框架"""
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.grid(row=1, column=0, padx=10, pady=10, sticky="ew")

        # 标题
        stats_title = ctk.CTkLabel(stats_frame, text="📈 统计信息", font=ctk.CTkFont(size=16, weight="bold"))
        stats_title.grid(row=0, column=0, columnspan=4, padx=10, pady=(10, 5), sticky="w")

        # 统计信息网格
        stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
        stats_grid.grid(row=1, column=0, columnspan=4, padx=10, pady=10, sticky="ew")

        # 配置网格权重
        for i in range(4):
            stats_grid.grid_columnconfigure(i, weight=1)

        # 总数
        self.total_label = ctk.CTkLabel(stats_grid, text="总数: 0", font=ctk.CTkFont(size=14))
        self.total_label.grid(row=0, column=0, padx=5, pady=5)

        # 成功
        self.success_label = ctk.CTkLabel(stats_grid, text="成功: 0", font=ctk.CTkFont(size=14), text_color="green")
        self.success_label.grid(row=0, column=1, padx=5, pady=5)

        # 失败
        self.failed_label = ctk.CTkLabel(stats_grid, text="失败: 0", font=ctk.CTkFont(size=14), text_color="red")
        self.failed_label.grid(row=0, column=2, padx=5, pady=5)

        # 跳过
        self.skipped_label = ctk.CTkLabel(stats_grid, text="跳过: 0", font=ctk.CTkFont(size=14), text_color="orange")
        self.skipped_label.grid(row=0, column=3, padx=5, pady=5)

    def _setup_log_frame_simple(self, parent):
        """简化版日志框架"""
        log_frame = ctk.CTkFrame(parent)
        log_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(1, weight=1)

        # 标题
        log_title = ctk.CTkLabel(log_frame, text="📝 运行日志", font=ctk.CTkFont(size=16, weight="bold"))
        log_title.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")

        # 日志文本框 - 高性能配置
        self.log_textbox = ctk.CTkTextbox(
            log_frame,
            height=400,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.log_textbox.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="nsew")

        # 性能优化：限制日志行数
        self.max_log_lines = 500  # 减少到500行以提高性能
        self.log_line_count = 0

    def _setup_left_panel(self):
        """设置左侧面板（设置区域）"""
        # 主题设置
        self._setup_theme_frame()
        # 登录状态
        self._setup_login_frame()
        # 下载设置
        self._setup_download_settings_frame()
        # 性能设置
        self._setup_performance_frame()
        # 控制按钮
        self._setup_control_frame()

    def _setup_right_panel(self, parent=None):
        """设置右侧面板（状态和日志）"""
        if parent is None:
            parent = self.right_panel

        # 进度显示
        self._setup_progress_frame(parent)
        # 统计信息
        self._setup_stats_frame(parent)
        # 日志显示
        self._setup_log_frame(parent)

    def _setup_theme_frame(self):
        """设置主题切换框架"""
        theme_frame = ctk.CTkFrame(self.main_scrollable_frame)
        theme_frame.grid(row=0, column=0, padx=10, pady=(0, 10), sticky="ew")
        theme_frame.grid_columnconfigure(2, weight=1)
        
        # 主题标签
        theme_label = ctk.CTkLabel(
            theme_frame, 
            text="界面主题:", 
            font=ctk.CTkFont(size=14, weight="bold")
        )
        theme_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")
        
        # 主题切换按钮
        self.theme_var = tk.StringVar(value="dark")
        theme_switch = ctk.CTkSegmentedButton(
            theme_frame,
            values=["light", "dark", "system"],
            variable=self.theme_var,
            command=self._change_theme
        )
        theme_switch.grid(row=0, column=1, padx=10, pady=10)
        theme_switch.set("dark")
        
        # 颜色主题选择
        color_label = ctk.CTkLabel(theme_frame, text="颜色主题:")
        color_label.grid(row=0, column=2, padx=(20, 10), pady=10, sticky="w")
        
        self.color_theme_var = tk.StringVar(value="blue")
        color_theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            values=["blue", "green", "dark-blue"],
            variable=self.color_theme_var,
            command=self._change_color_theme
        )
        color_theme_menu.grid(row=0, column=3, padx=10, pady=10)
    
    def _setup_login_frame(self):
        """设置登录状态框架"""
        login_frame = ctk.CTkFrame(self.left_panel)
        login_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")
        login_frame.grid_columnconfigure(2, weight=1)
        
        # 标题
        login_title = ctk.CTkLabel(
            login_frame, 
            text="🔐 登录状态", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        login_title.grid(row=0, column=0, columnspan=4, padx=10, pady=(10, 5), sticky="w")
        
        # 状态显示
        self.login_status_label = ctk.CTkLabel(
            login_frame, 
            text="检查中...", 
            font=ctk.CTkFont(size=14),
            text_color="orange"
        )
        self.login_status_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        # 检查登录按钮
        check_login_btn = ctk.CTkButton(
            login_frame,
            text="检查登录",
            command=self.check_login_status,
            width=100
        )
        check_login_btn.grid(row=1, column=1, padx=10, pady=5)
        
        # 重新登录按钮
        relogin_btn = ctk.CTkButton(
            login_frame,
            text="重新登录",
            command=self.start_login,
            width=100
        )
        relogin_btn.grid(row=1, column=2, padx=10, pady=5)
    
    def _setup_download_settings_frame(self):
        """设置下载设置框架"""
        settings_frame = ctk.CTkFrame(self.left_panel)
        settings_frame.grid(row=2, column=0, padx=10, pady=(0, 10), sticky="ew")
        settings_frame.grid_columnconfigure(1, weight=1)
        
        # 标题
        settings_title = ctk.CTkLabel(
            settings_frame, 
            text="📥 下载设置", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        settings_title.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")
        
        row = 1
        
        # 下载模式选择
        mode_label = ctk.CTkLabel(settings_frame, text="下载模式:")
        mode_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        
        self.download_mode_var = tk.StringVar(value=self.download_config.download_mode.value)
        mode_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        mode_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")
        
        # 下载模式单选按钮
        self.mode_buttons = []
        modes = [
            ("关注画师新作", "date"),
            ("排行榜", "ranking"),
            ("搜索", "search"),
            ("用户", "user")
        ]
        
        for i, (text, value) in enumerate(modes):
            btn = ctk.CTkRadioButton(
                mode_frame,
                text=text,
                variable=self.download_mode_var,
                value=value,
                command=self._on_download_mode_changed
            )
            btn.grid(row=0, column=i, padx=10, pady=5, sticky="w")
            self.mode_buttons.append(btn)
        
        row += 1

        # 搜索关键词
        self.search_keyword_label = ctk.CTkLabel(settings_frame, text="搜索关键词:")
        self.search_keyword_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        self.search_keyword_var = tk.StringVar(value=self.download_config.search_keyword)
        self.search_keyword_entry = ctk.CTkEntry(
            settings_frame,
            textvariable=self.search_keyword_var,
            placeholder_text="输入搜索关键词..."
        )
        self.search_keyword_entry.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        row += 1

        # 搜索配置
        search_config_label = ctk.CTkLabel(settings_frame, text="搜索配置:")
        search_config_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        search_config_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        search_config_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")
        search_config_frame.grid_columnconfigure(0, weight=1)
        search_config_frame.grid_columnconfigure(1, weight=1)
        search_config_frame.grid_columnconfigure(2, weight=1)

        # 搜索种类
        self.search_category_var = tk.StringVar(value=self.download_config.search_config.category.value)
        search_category_menu = ctk.CTkOptionMenu(
            search_config_frame,
            values=[cat.value for cat in SearchCategory],
            variable=self.search_category_var
        )
        search_category_menu.grid(row=0, column=0, padx=5, pady=2, sticky="ew")

        # 收藏数过滤
        self.search_bookmark_var = tk.StringVar(value=str(self.download_config.search_config.bookmark_count.value))
        bookmark_values = ["不启用"] + [f"{count.value}users入り" for count in SearchBookmarkCount if count != SearchBookmarkCount.DISABLED]
        search_bookmark_menu = ctk.CTkOptionMenu(
            search_config_frame,
            values=bookmark_values,
            variable=self.search_bookmark_var
        )
        search_bookmark_menu.grid(row=0, column=1, padx=5, pady=2, sticky="ew")

        # 内容模式
        self.search_content_mode_var = tk.StringVar(value=self.download_config.search_config.content_mode.value)
        search_content_menu = ctk.CTkOptionMenu(
            search_config_frame,
            values=[mode.value for mode in SearchContentMode],
            variable=self.search_content_mode_var
        )
        search_content_menu.grid(row=0, column=2, padx=5, pady=2, sticky="ew")

        row += 1

        # 用户ID
        self.user_id_label = ctk.CTkLabel(settings_frame, text="用户ID:")
        self.user_id_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        self.user_id_var = tk.StringVar(value=str(self.download_config.user_id) if self.download_config.user_id else "")
        self.user_id_entry = ctk.CTkEntry(
            settings_frame,
            textvariable=self.user_id_var,
            placeholder_text="输入用户ID..."
        )
        self.user_id_entry.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        row += 1

        # 页码范围
        page_label = ctk.CTkLabel(settings_frame, text="页码范围:")
        page_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        page_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        page_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        self.start_page_var = tk.StringVar(value=str(self.download_config.start_page))
        self.end_page_var = tk.StringVar(value=str(self.download_config.end_page))

        self.start_page_entry = ctk.CTkEntry(page_frame, textvariable=self.start_page_var, width=80)
        self.start_page_entry.grid(row=0, column=0, padx=5, pady=2)

        to_label = ctk.CTkLabel(page_frame, text="到")
        to_label.grid(row=0, column=1, padx=5, pady=2)

        self.end_page_entry = ctk.CTkEntry(page_frame, textvariable=self.end_page_var, width=80)
        self.end_page_entry.grid(row=0, column=2, padx=5, pady=2)

        self.page_note_label = ctk.CTkLabel(page_frame, text="页")
        self.page_note_label.grid(row=0, column=3, padx=5, pady=2)

        row += 1

        # 天数设置
        self.days_label = ctk.CTkLabel(settings_frame, text="下载天数:")
        self.days_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        days_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        days_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        self.days_var = tk.StringVar(value=str(self.download_config.days))
        self.days_entry = ctk.CTkEntry(days_frame, textvariable=self.days_var, width=80)
        self.days_entry.grid(row=0, column=0, padx=5, pady=2)

        self.days_note_label = ctk.CTkLabel(days_frame, text="天（日期模式）")
        self.days_note_label.grid(row=0, column=1, padx=5, pady=2)

        row += 1

        # 关注画师模式设置
        self.date_mode_label = ctk.CTkLabel(settings_frame, text="关注画师模式:")
        self.date_mode_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        date_mode_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        date_mode_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        self.date_mode_var = tk.StringVar(value=self.download_config.date_mode.value)
        date_mode_btn1 = ctk.CTkRadioButton(
            date_mode_frame, text="按日期范围", variable=self.date_mode_var, value="by_date_range"
        )
        date_mode_btn1.grid(row=0, column=0, padx=10, pady=2, sticky="w")

        date_mode_btn2 = ctk.CTkRadioButton(
            date_mode_frame, text="按页码范围", variable=self.date_mode_var, value="by_page_range"
        )
        date_mode_btn2.grid(row=0, column=1, padx=10, pady=2, sticky="w")

        row += 1

        # 动图处理
        gif_label = ctk.CTkLabel(settings_frame, text="动图处理:")
        gif_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        gif_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        gif_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        self.gif_mode_var = tk.StringVar(value=self.download_config.gif_mode.value)
        gif_modes = [
            ("仅GIF", "gif_only"),
            ("仅帧", "frames_only"),
            ("都保存", "both")
        ]

        for i, (text, value) in enumerate(gif_modes):
            btn = ctk.CTkRadioButton(
                gif_frame, text=text, variable=self.gif_mode_var, value=value
            )
            btn.grid(row=0, column=i, padx=10, pady=2, sticky="w")

        row += 1

        # 文件分类
        classify_label = ctk.CTkLabel(settings_frame, text="文件分类:")
        classify_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        classify_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        classify_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")

        self.classify_mode_var = tk.StringVar(value=self.download_config.classify_mode.value)
        classify_modes = [
            ("按日期", "by_date"),
            ("按作者", "by_author"),
            ("不分类", "flat")
        ]

        for i, (text, value) in enumerate(classify_modes):
            btn = ctk.CTkRadioButton(
                classify_frame, text=text, variable=self.classify_mode_var, value=value
            )
            btn.grid(row=0, column=i, padx=10, pady=2, sticky="w")

        row += 1

        # 保存路径设置
        self._setup_path_settings(settings_frame, row)

    def _setup_path_settings(self, parent, start_row):
        """设置保存路径配置"""
        row = start_row

        # 画师新作路径
        self.date_path_label = ctk.CTkLabel(parent, text="画师新作路径:")
        self.date_path_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        date_path_frame = ctk.CTkFrame(parent, fg_color="transparent")
        date_path_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")
        date_path_frame.grid_columnconfigure(0, weight=1)

        self.date_path_var = tk.StringVar(value=self.download_config.save_path)
        self.date_path_entry = ctk.CTkEntry(date_path_frame, textvariable=self.date_path_var)
        self.date_path_entry.grid(row=0, column=0, padx=5, pady=2, sticky="ew")

        date_path_btn = ctk.CTkButton(
            date_path_frame, text="浏览", command=self._browse_date_folder, width=80
        )
        date_path_btn.grid(row=0, column=1, padx=5, pady=2)

        row += 1

        # 搜索结果路径
        self.search_path_label = ctk.CTkLabel(parent, text="搜索结果路径:")
        self.search_path_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        search_path_frame = ctk.CTkFrame(parent, fg_color="transparent")
        search_path_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")
        search_path_frame.grid_columnconfigure(0, weight=1)

        search_path = getattr(self.download_config, 'search_save_path', '') or "pixiv_search"
        self.search_path_var = tk.StringVar(value=search_path)
        self.search_path_entry = ctk.CTkEntry(search_path_frame, textvariable=self.search_path_var)
        self.search_path_entry.grid(row=0, column=0, padx=5, pady=2, sticky="ew")

        search_path_btn = ctk.CTkButton(
            search_path_frame, text="浏览", command=self._browse_search_folder, width=80
        )
        search_path_btn.grid(row=0, column=1, padx=5, pady=2)

        row += 1

        # 用户作品路径
        self.user_path_label = ctk.CTkLabel(parent, text="用户作品路径:")
        self.user_path_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        user_path_frame = ctk.CTkFrame(parent, fg_color="transparent")
        user_path_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")
        user_path_frame.grid_columnconfigure(0, weight=1)

        user_path = getattr(self.download_config, 'user_save_path', '') or "pixiv_users"
        self.user_path_var = tk.StringVar(value=user_path)
        self.user_path_entry = ctk.CTkEntry(user_path_frame, textvariable=self.user_path_var)
        self.user_path_entry.grid(row=0, column=0, padx=5, pady=2, sticky="ew")

        user_path_btn = ctk.CTkButton(
            user_path_frame, text="浏览", command=self._browse_user_folder, width=80
        )
        user_path_btn.grid(row=0, column=1, padx=5, pady=2)

        row += 1

        # 排行榜路径
        self.ranking_path_label = ctk.CTkLabel(parent, text="排行榜路径:")
        self.ranking_path_label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        ranking_path_frame = ctk.CTkFrame(parent, fg_color="transparent")
        ranking_path_frame.grid(row=row, column=1, padx=10, pady=5, sticky="ew")
        ranking_path_frame.grid_columnconfigure(0, weight=1)

        ranking_path = self.download_config.ranking_config.custom_save_path or "pixiv_ranking"
        self.ranking_path_var = tk.StringVar(value=ranking_path)
        self.ranking_path_entry = ctk.CTkEntry(ranking_path_frame, textvariable=self.ranking_path_var)
        self.ranking_path_entry.grid(row=0, column=0, padx=5, pady=2, sticky="ew")

        ranking_path_btn = ctk.CTkButton(
            ranking_path_frame, text="浏览", command=self._browse_ranking_folder, width=80
        )
        ranking_path_btn.grid(row=0, column=1, padx=5, pady=2)

    def _setup_performance_frame(self):
        """设置性能设置框架"""
        perf_frame = ctk.CTkFrame(self.left_panel)
        perf_frame.grid(row=3, column=0, padx=10, pady=(0, 10), sticky="ew")
        perf_frame.grid_columnconfigure(1, weight=1)

        # 标题
        perf_title = ctk.CTkLabel(
            perf_frame,
            text="⚡ 性能设置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        perf_title.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")

        # 并发数设置
        concurrent_label = ctk.CTkLabel(perf_frame, text="并发数:")
        concurrent_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        self.concurrent_var = tk.StringVar(value=str(self.spider_config.concurrent_downloads))
        concurrent_entry = ctk.CTkEntry(perf_frame, textvariable=self.concurrent_var, width=100)
        concurrent_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # 延迟设置
        delay_label = ctk.CTkLabel(perf_frame, text="重试延迟(秒):")
        delay_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")

        self.delay_var = tk.StringVar(value=str(self.spider_config.retry_delay))
        delay_entry = ctk.CTkEntry(perf_frame, textvariable=self.delay_var, width=100)
        delay_entry.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        # 性能模式设置
        perf_mode_label = ctk.CTkLabel(perf_frame, text="性能模式:")
        perf_mode_label.grid(row=3, column=0, padx=10, pady=5, sticky="w")

        self.performance_mode_var = tk.StringVar(value="balanced")
        perf_mode_menu = ctk.CTkOptionMenu(
            perf_frame,
            values=["high_performance", "balanced", "low_resource"],
            variable=self.performance_mode_var,
            command=self._on_performance_mode_changed
        )
        perf_mode_menu.grid(row=3, column=1, padx=10, pady=5, sticky="w")

    def _setup_control_frame(self):
        """设置控制按钮框架"""
        control_frame = ctk.CTkFrame(self.left_panel)
        control_frame.grid(row=4, column=0, padx=10, pady=(0, 10), sticky="ew")

        # 标题
        control_title = ctk.CTkLabel(
            control_frame,
            text="🎮 控制面板",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_title.grid(row=0, column=0, columnspan=4, padx=10, pady=(10, 5), sticky="w")

        # 按钮容器
        button_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        button_frame.grid(row=1, column=0, columnspan=4, padx=10, pady=10, sticky="ew")

        # 开始下载按钮
        self.start_btn = ctk.CTkButton(
            button_frame,
            text="🚀 开始下载",
            command=self.start_download,
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.start_btn.grid(row=0, column=0, padx=10, pady=5)

        # 停止下载按钮
        self.stop_btn = ctk.CTkButton(
            button_frame,
            text="⏹️ 停止下载",
            command=self.stop_download,
            width=120,
            height=40,
            state="disabled",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.stop_btn.grid(row=0, column=1, padx=10, pady=5)

        # 保存设置按钮
        save_btn = ctk.CTkButton(
            button_frame,
            text="💾 保存设置",
            command=self.save_settings,
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        save_btn.grid(row=0, column=2, padx=10, pady=5)

        # 清空日志按钮
        clear_btn = ctk.CTkButton(
            button_frame,
            text="🗑️ 清空日志",
            command=self.clear_log,
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        clear_btn.grid(row=0, column=3, padx=10, pady=5)

    def _setup_progress_frame(self, parent=None):
        """设置进度显示框架"""
        if parent is None:
            parent = self.right_panel
        progress_frame = ctk.CTkFrame(parent)
        progress_frame.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="ew")
        progress_frame.grid_columnconfigure(1, weight=1)

        # 标题
        progress_title = ctk.CTkLabel(
            progress_frame,
            text="📊 下载进度",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        progress_title.grid(row=0, column=0, columnspan=2, padx=10, pady=(10, 5), sticky="w")

        # 当前状态
        status_label = ctk.CTkLabel(progress_frame, text="当前状态:")
        status_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        self.status_label = ctk.CTkLabel(
            progress_frame,
            text="等待开始...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # 进度条
        progress_bar_label = ctk.CTkLabel(progress_frame, text="总体进度:")
        progress_bar_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")

        self.progress_bar = ctk.CTkProgressBar(progress_frame)
        self.progress_bar.grid(row=2, column=1, padx=10, pady=5, sticky="ew")
        self.progress_bar.set(0)

        # 进度百分比
        self.progress_label = ctk.CTkLabel(progress_frame, text="0%")
        self.progress_label.grid(row=3, column=1, padx=10, pady=5, sticky="w")

    def _setup_stats_frame(self, parent=None):
        """设置统计信息框架"""
        if parent is None:
            parent = self.right_panel
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

        # 标题
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 统计信息",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.grid(row=0, column=0, columnspan=4, padx=10, pady=(10, 5), sticky="w")

        # 统计信息网格
        stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
        stats_grid.grid(row=1, column=0, columnspan=4, padx=10, pady=10, sticky="ew")

        # 配置网格权重
        for i in range(4):
            stats_grid.grid_columnconfigure(i, weight=1)

        # 总数
        total_frame = ctk.CTkFrame(stats_grid)
        total_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(total_frame, text="总数", font=ctk.CTkFont(weight="bold")).pack(pady=2)
        self.total_label = ctk.CTkLabel(total_frame, text="0", font=ctk.CTkFont(size=18))
        self.total_label.pack(pady=2)

        # 成功
        success_frame = ctk.CTkFrame(stats_grid)
        success_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(success_frame, text="成功", font=ctk.CTkFont(weight="bold"), text_color="green").pack(pady=2)
        self.success_label = ctk.CTkLabel(success_frame, text="0", font=ctk.CTkFont(size=18), text_color="green")
        self.success_label.pack(pady=2)

        # 失败
        failed_frame = ctk.CTkFrame(stats_grid)
        failed_frame.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(failed_frame, text="失败", font=ctk.CTkFont(weight="bold"), text_color="red").pack(pady=2)
        self.failed_label = ctk.CTkLabel(failed_frame, text="0", font=ctk.CTkFont(size=18), text_color="red")
        self.failed_label.pack(pady=2)

        # 跳过
        skipped_frame = ctk.CTkFrame(stats_grid)
        skipped_frame.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(skipped_frame, text="跳过", font=ctk.CTkFont(weight="bold"), text_color="orange").pack(pady=2)
        self.skipped_label = ctk.CTkLabel(skipped_frame, text="0", font=ctk.CTkFont(size=18), text_color="orange")
        self.skipped_label.pack(pady=2)

    def _setup_log_frame(self, parent=None):
        """设置日志框架"""
        if parent is None:
            parent = self.right_panel
        log_frame = ctk.CTkFrame(parent)
        log_frame.grid(row=2, column=0, padx=10, pady=(5, 10), sticky="nsew")
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(1, weight=1)

        # 标题
        log_title = ctk.CTkLabel(
            log_frame,
            text="📝 运行日志",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_title.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")

        # 日志文本框 - 优化性能设置
        self.log_textbox = ctk.CTkTextbox(
            log_frame,
            height=200,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.log_textbox.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="nsew")

        # 性能优化：限制日志行数
        self.max_log_lines = 1000
        self.log_line_count = 0

    def _change_theme(self, value):
        """切换主题"""
        ctk.set_appearance_mode(value)
    
    def _change_color_theme(self, value):
        """切换颜色主题"""
        ctk.set_default_color_theme(value)
        # 重启应用以应用新主题
        messagebox.showinfo("主题更改", "颜色主题将在重启应用后生效")

    def _on_performance_mode_changed(self, value):
        """性能模式改变时的处理"""
        try:
            if value == "high_performance":
                # 高性能模式：更快的更新频率，更多资源使用
                self.progress_update_interval = 0.05
                self.max_log_lines = 2000
                self.log("🚀 切换到高性能模式")
            elif value == "balanced":
                # 平衡模式：默认设置
                self.progress_update_interval = 0.1
                self.max_log_lines = 1000
                self.log("⚖️ 切换到平衡模式")
            elif value == "low_resource":
                # 低资源模式：较慢的更新频率，节省资源
                self.progress_update_interval = 0.2
                self.max_log_lines = 500
                self.log("🔋 切换到低资源模式")
        except Exception as e:
            self.log(f"切换性能模式时出错: {e}")
    
    def _on_download_mode_changed(self):
        """下载模式改变时的处理"""
        self._update_page_settings()
        self._hide_all_config_widgets()
        self._show_relevant_widgets()

    def _update_page_settings(self):
        """根据下载模式更新页码设置"""
        current_mode = self.download_mode_var.get()

        if current_mode == "ranking":
            # 排行榜模式：固定页码1-2
            self.start_page_var.set("1")
            self.end_page_var.set("2")
            self.start_page_entry.configure(state="disabled")
            self.end_page_entry.configure(state="disabled")
            self.page_note_label.configure(text="页 (排行榜固定1-2页)")
        else:
            # 其他模式：启用页码设置
            self.start_page_entry.configure(state="normal")
            self.end_page_entry.configure(state="normal")
            self.page_note_label.configure(text="页")

    def _hide_all_config_widgets(self):
        """隐藏所有配置组件"""
        try:
            # 隐藏搜索相关
            if hasattr(self, 'search_keyword_label'):
                self.search_keyword_label.grid_remove()
            if hasattr(self, 'search_keyword_entry'):
                self.search_keyword_entry.grid_remove()

            # 隐藏用户ID
            if hasattr(self, 'user_id_label'):
                self.user_id_label.grid_remove()
            if hasattr(self, 'user_id_entry'):
                self.user_id_entry.grid_remove()

            # 隐藏天数设置
            if hasattr(self, 'days_label'):
                self.days_label.grid_remove()
            if hasattr(self, 'days_entry'):
                self.days_entry.grid_remove()
            if hasattr(self, 'days_note_label'):
                self.days_note_label.grid_remove()

            # 隐藏关注画师模式
            if hasattr(self, 'date_mode_label'):
                self.date_mode_label.grid_remove()
        except Exception as e:
            self.log(f"隐藏配置组件时出错: {e}")

    def _show_relevant_widgets(self):
        """显示相关的配置组件"""
        try:
            current_mode = self.download_mode_var.get()

            if current_mode == "search":
                # 搜索模式：显示搜索关键词
                if hasattr(self, 'search_keyword_label'):
                    self.search_keyword_label.grid()
                if hasattr(self, 'search_keyword_entry'):
                    self.search_keyword_entry.grid()
            elif current_mode == "user":
                # 用户模式：显示用户ID
                if hasattr(self, 'user_id_label'):
                    self.user_id_label.grid()
                if hasattr(self, 'user_id_entry'):
                    self.user_id_entry.grid()
            elif current_mode == "date":
                # 日期模式：显示天数和关注画师模式
                if hasattr(self, 'days_label'):
                    self.days_label.grid()
                if hasattr(self, 'days_entry'):
                    self.days_entry.grid()
                if hasattr(self, 'days_note_label'):
                    self.days_note_label.grid()
                if hasattr(self, 'date_mode_label'):
                    self.date_mode_label.grid()
        except Exception as e:
            self.log(f"显示配置组件时出错: {e}")

    # 文件夹浏览方法
    def _browse_date_folder(self):
        """浏览画师新作保存文件夹"""
        folder = filedialog.askdirectory(title="选择画师新作保存文件夹")
        if folder:
            self.date_path_var.set(folder)

    def _browse_search_folder(self):
        """浏览搜索结果保存文件夹"""
        folder = filedialog.askdirectory(title="选择搜索结果保存文件夹")
        if folder:
            self.search_path_var.set(folder)

    def _browse_user_folder(self):
        """浏览用户作品保存文件夹"""
        folder = filedialog.askdirectory(title="选择用户作品保存文件夹")
        if folder:
            self.user_path_var.set(folder)

    def _browse_ranking_folder(self):
        """浏览排行榜保存文件夹"""
        folder = filedialog.askdirectory(title="选择排行榜保存文件夹")
        if folder:
            self.ranking_path_var.set(folder)

    def setup_callbacks(self):
        """设置回调函数"""
        self.spider.set_progress_callback(self.on_progress_update)
        self.spider.set_status_callback(self.on_status_update)

    def load_settings(self):
        """加载设置"""
        try:
            # 初始化时更新界面状态
            self._on_download_mode_changed()
        except Exception as e:
            self.log(f"加载设置时出错: {e}")

    def check_login_status(self):
        """检查登录状态"""
        def check_in_thread():
            try:
                self.login_status_label.configure(text="检查中...", text_color="orange")

                # 检查cookie文件是否存在
                cookie_file = os.path.join("cookies", "pixiv_cookies.pkl")
                if os.path.exists(cookie_file):
                    # 尝试验证登录状态
                    success = self.spider.auth_service.verify_login()
                    if success:
                        self.is_authenticated = True
                        self.root.after(0, lambda: self.login_status_label.configure(
                            text="✅ 已登录", text_color="green"
                        ))
                        self.log("✅ 登录状态验证成功")
                    else:
                        self.is_authenticated = False
                        self.root.after(0, lambda: self.login_status_label.configure(
                            text="❌ 登录已过期", text_color="red"
                        ))
                        self.log("❌ 登录状态已过期，请重新登录")
                else:
                    self.is_authenticated = False
                    self.root.after(0, lambda: self.login_status_label.configure(
                        text="❌ 未登录", text_color="red"
                    ))
                    self.log("🔐 没有找到cookie文件，需要登录")

            except Exception as e:
                self.is_authenticated = False
                self.root.after(0, lambda: self.login_status_label.configure(
                    text="❌ 检查失败", text_color="red"
                ))
                self.log(f"❌ 登录状态检查失败: {e}")

        threading.Thread(target=check_in_thread, daemon=True).start()

    def start_login(self):
        """开始登录"""
        try:
            from .modern_login_dialog import ModernLoginDialog

            dialog = ModernLoginDialog(self.root, self.spider.auth_service)
            success, cookies = dialog.show()

            if success and cookies:
                self.is_authenticated = True
                self.login_status_label.configure(text="✅ 已登录", text_color="green")
                self.log("✅ 登录成功！")
            else:
                self.is_authenticated = False
                self.login_status_label.configure(text="❌ 登录失败", text_color="red")
                self.log("❌ 登录失败或被取消")

        except Exception as e:
            self.log(f"❌ 登录过程出错: {e}")

    def on_progress_update(self, progress_data):
        """进度更新回调"""
        try:
            # 性能优化：限制更新频率和检查窗口状态
            current_time = time.time()
            if (current_time - self.last_progress_update < self.progress_update_interval or
                not self.is_window_mapped):
                return
            self.last_progress_update = current_time

            if isinstance(progress_data, dict):
                # 批量更新UI以减少重绘次数
                updates = []

                # 更新进度条
                if 'progress' in progress_data:
                    progress = progress_data['progress']
                    updates.append(lambda: self.progress_bar.set(progress / 100.0))
                    updates.append(lambda: self.progress_label.configure(text=f"{progress:.1f}%"))

                # 更新统计信息
                if 'stats' in progress_data:
                    stats = progress_data['stats']
                    updates.append(lambda: self.total_label.configure(text=f"总数: {stats.get('total', 0)}"))
                    updates.append(lambda: self.success_label.configure(text=f"成功: {stats.get('success', 0)}"))
                    updates.append(lambda: self.failed_label.configure(text=f"失败: {stats.get('failed', 0)}"))
                    updates.append(lambda: self.skipped_label.configure(text=f"跳过: {stats.get('skipped', 0)}"))

                # 批量执行更新
                for update in updates:
                    update()

        except Exception as e:
            self.log(f"更新进度时出错: {e}")

    def on_status_update(self, status_data):
        """状态更新回调"""
        try:
            if isinstance(status_data, str):
                self.status_label.configure(text=status_data)
                self.log(status_data)
            elif isinstance(status_data, dict) and 'message' in status_data:
                self.status_label.configure(text=status_data['message'])
                self.log(status_data['message'])
        except Exception as e:
            self.log(f"更新状态时出错: {e}")

    def start_download(self):
        """开始下载"""
        if not self.is_authenticated:
            messagebox.showerror("错误", "请先登录Pixiv账号")
            return

        if self.is_running:
            messagebox.showwarning("警告", "下载任务正在运行中")
            return

        try:
            # 保存当前设置
            self.save_settings()

            # 更新UI状态
            self.is_running = True
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.status_label.configure(text="正在启动下载...")

            # 重置统计信息
            self.stats = {
                'start_time': datetime.now(),
                'total': 0,
                'completed': 0,
                'success': 0,
                'failed': 0,
                'skipped': 0
            }

            # 在后台线程中启动下载
            self.download_thread = threading.Thread(target=self._download_worker, daemon=True)
            self.download_thread.start()

            self.log("🚀 下载任务已启动")

        except Exception as e:
            self.is_running = False
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.log(f"❌ 启动下载失败: {e}")
            messagebox.showerror("错误", f"启动下载失败: {e}")

    def stop_download(self):
        """停止下载"""
        if not self.is_running:
            return

        try:
            self.is_running = False
            self.spider.stop()

            # 更新UI状态
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.status_label.configure(text="正在停止...")

            self.log("⏹️ 正在停止下载任务...")

        except Exception as e:
            self.log(f"❌ 停止下载时出错: {e}")

    def _download_worker(self):
        """下载工作线程"""
        try:
            # 根据下载模式执行相应的下载任务
            mode = self.download_mode_var.get()

            if mode == "date":
                self.spider.download_following_new_works()
            elif mode == "ranking":
                self.spider.download_ranking()
            elif mode == "search":
                keyword = self.search_keyword_var.get().strip()
                if not keyword:
                    raise ValueError("搜索关键词不能为空")
                self.spider.download_search_results(keyword)
            elif mode == "user":
                user_id = self.user_id_var.get().strip()
                if not user_id:
                    raise ValueError("用户ID不能为空")
                self.spider.download_user_works(int(user_id))

            # 下载完成
            self.root.after(0, self._download_completed)

        except Exception as e:
            self.root.after(0, lambda: self._download_error(e))

    def _download_completed(self):
        """下载完成处理"""
        self.is_running = False
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.status_label.configure(text="下载完成")
        self.log("✅ 下载任务完成")
        messagebox.showinfo("完成", "下载任务已完成！")

    def _download_error(self, error):
        """下载错误处理"""
        self.is_running = False
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.status_label.configure(text="下载出错")
        self.log(f"❌ 下载出错: {error}")
        messagebox.showerror("错误", f"下载过程中出错: {error}")

    def save_settings(self):
        """保存设置"""
        try:
            # 更新下载配置
            self.download_config.download_mode = DownloadMode(self.download_mode_var.get())
            self.download_config.search_keyword = self.search_keyword_var.get()
            self.download_config.start_page = int(self.start_page_var.get() or 1)
            self.download_config.end_page = int(self.end_page_var.get() or 1)
            self.download_config.days = int(self.days_var.get() or 7)
            self.download_config.gif_mode = GifMode(self.gif_mode_var.get())
            self.download_config.classify_mode = ClassifyMode(self.classify_mode_var.get())
            self.download_config.save_path = self.date_path_var.get()

            if self.user_id_var.get().strip():
                self.download_config.user_id = int(self.user_id_var.get())

            # 更新性能配置
            self.spider_config.concurrent_downloads = int(self.concurrent_var.get() or 3)
            self.spider_config.retry_delay = float(self.delay_var.get() or 1.0)

            # 保存配置
            self.config_manager.save_download_config(self.download_config)
            self.config_manager.save_spider_config(self.spider_config)

            self.log("💾 设置已保存")
            messagebox.showinfo("成功", "设置已保存")

        except Exception as e:
            self.log(f"❌ 保存设置失败: {e}")
            messagebox.showerror("错误", f"保存设置失败: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_textbox.delete("1.0", "end")
        self.log("🗑️ 日志已清空")

    def log(self, message: str):
        """添加日志消息"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"

            # 在主线程中更新UI
            if threading.current_thread() == threading.main_thread():
                self._add_log_message(log_message)
            else:
                self.root.after(0, lambda: self._add_log_message(log_message))

        except Exception as e:
            print(f"日志记录失败: {e}")

    def _add_log_message(self, message: str):
        """在主线程中添加日志消息"""
        try:
            # 性能优化：检查窗口状态和限制日志行数
            if not self.is_window_mapped:
                return

            # 限制日志行数
            if self.log_line_count >= self.max_log_lines:
                # 删除前面的行以保持性能
                self.log_textbox.delete("1.0", "20.0")  # 删除前20行
                self.log_line_count -= 20

            # 批量插入以减少重绘
            self.log_textbox.configure(state="normal")
            self.log_textbox.insert("end", message)
            self.log_line_count += 1

            # 减少滚动频率
            if self.log_line_count % 5 == 0:  # 每5行滚动一次
                self.log_textbox.see("end")

        except Exception as e:
            print(f"添加日志消息失败: {e}")

    def run(self):
        """运行GUI应用"""
        try:
            # 绑定窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 启动主循环
            self.root.mainloop()
            
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"GUI运行错误: {e}")
            messagebox.showerror("运行错误", f"GUI运行时出错: {e}")
    
    def on_closing(self):
        """窗口关闭时的清理工作"""
        try:
            # 如果正在下载，先停止
            if self.is_running:
                self.logger.info("检测到窗口关闭，正在停止下载...")
                # 这里会添加停止下载逻辑
            
            # 清理爬虫资源
            if hasattr(self.spider, 'cleanup_resources'):
                self.spider.cleanup_resources()
            
            self.logger.info("程序退出，资源已清理")
            
        except Exception as e:
            print(f"关闭时清理资源出错: {e}")
        finally:
            # 销毁窗口
            self.root.destroy()


def create_modern_gui(config_manager: Optional[ConfigManager] = None) -> ModernPixivSpiderGUI:
    """
    创建现代化GUI应用程序
    
    Args:
        config_manager: 配置管理器
        
    Returns:
        ModernPixivSpiderGUI: 现代化GUI应用实例
    """
    return ModernPixivSpiderGUI(config_manager)
