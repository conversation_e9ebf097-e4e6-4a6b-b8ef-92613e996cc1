@echo off
title Pixiv Spider - High Performance GUI Launcher

echo.
echo Pixiv Spider - High Performance GUI Launcher
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found, please install Python 3.7+
    pause
    exit /b 1
)

echo Python is installed
echo.

REM Check if dependencies are installed
echo Checking dependencies...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo CustomTkinter not installed, installing...
    pip install customtkinter>=5.2.0
    if errorlevel 1 (
        echo CustomTkinter installation failed
        pause
        exit /b 1
    )
)

echo Dependencies check completed
echo.

REM Launch high performance GUI
echo Starting high performance GUI...
python run_high_performance_gui.py

REM If program exits with error, pause to see error message
if errorlevel 1 (
    echo.
    echo Program exited with error
    pause
)

echo.
echo Program exited
pause
