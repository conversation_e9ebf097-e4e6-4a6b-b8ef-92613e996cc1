#!/usr/bin/env python3
"""
Pixiv Spider 高性能GUI启动脚本

专门针对性能优化的启动脚本，减少卡顿和提高响应速度
"""

import sys
import os
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def optimize_system():
    """系统级性能优化"""
    try:
        # 设置进程优先级
        import psutil
        current_process = psutil.Process()
        current_process.nice(psutil.HIGH_PRIORITY_CLASS if os.name == 'nt' else -10)
    except:
        pass
    
    # 设置环境变量优化
    os.environ['PYTHONOPTIMIZE'] = '1'  # 启用Python优化
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'  # 不生成.pyc文件


def main():
    """主函数"""
    print("🚀 Pixiv Spider - 高性能GUI启动器")
    print("=" * 50)
    
    # 系统优化
    optimize_system()
    
    try:
        # 设置日志
        logging.basicConfig(
            level=logging.WARNING,  # 减少日志输出
            format="%(asctime)s - %(levelname)s - %(message)s"
        )
        
        print("🔄 正在导入模块...")
        
        try:
            # 检查CustomTkinter是否可用
            import customtkinter as ctk
            print("✅ CustomTkinter 已安装")
            
            # 设置高性能模式
            ctk.set_appearance_mode("dark")  # 暗色主题性能更好
            
            from pixiv_spider.gui import create_modern_gui
            from pixiv_spider.config import ConfigManager
        except ImportError as e:
            print(f"❌ 导入错误: {e}")
            print("\n详细错误信息:")
            import traceback
            traceback.print_exc()
            print("\n📋 可能的解决方案:")
            print("1. 检查是否安装了所有必需的依赖包")
            print("2. 运行: pip install -r requirements.txt")
            print("3. 确保 customtkinter 已安装: pip install customtkinter")
            input("\n按回车键退出...")
            return
        
        print("✅ 模块导入成功")
        
        # 创建配置管理器
        print("🔧 正在初始化配置...")
        config_manager = ConfigManager()
        
        # 创建高性能GUI应用
        print("🚀 正在启动高性能GUI界面...")
        app = create_modern_gui(config_manager)
        
        # 设置高性能模式
        if hasattr(app, 'performance_mode_var'):
            app.performance_mode_var.set("high_performance")
            app._on_performance_mode_changed("high_performance")
        
        print("🎉 高性能GUI启动成功！")
        print("💡 提示: 已自动启用高性能模式")
        print("🚀 享受流畅的界面体验！")
        print()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")


if __name__ == "__main__":
    main()
