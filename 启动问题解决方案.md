# Pixiv Spider 现代化GUI启动问题解决方案

## 🚀 启动方法

### 方法1: 使用修复后的批处理文件
```bash
双击运行: run_modern_gui.bat
```

### 方法2: 使用简化版批处理文件
```bash
双击运行: start_modern_gui.bat
```

### 方法3: 使用命令行
```bash
# 在项目目录下打开命令提示符或PowerShell
python run_modern_gui.py
```

### 方法4: 在PowerShell中运行
```powershell
# 注意：在PowerShell中需要使用 .\ 前缀
.\run_modern_gui.bat
```

## 🔧 常见问题解决

### 问题1: "无法将项识别为cmdlet"
**原因**: PowerShell需要明确指定当前目录的文件
**解决方案**: 
```powershell
.\run_modern_gui.bat
```

### 问题2: 中文字符显示乱码
**原因**: 系统编码问题
**解决方案**: 
1. 使用 `start_modern_gui.bat` (简化版，无中文)
2. 或直接运行: `python run_modern_gui.py`

### 问题3: Python未找到
**解决方案**:
1. 安装Python 3.7+ 从 https://www.python.org/
2. 安装时勾选 "Add Python to PATH"
3. 重启命令提示符

### 问题4: CustomTkinter未安装
**解决方案**:
```bash
pip install customtkinter>=5.2.0
```

### 问题5: 权限问题
**解决方案**:
1. 右键点击bat文件
2. 选择"以管理员身份运行"

## 📋 系统要求检查

### 检查Python安装
```bash
python --version
```
应该显示Python 3.7或更高版本

### 检查pip可用性
```bash
pip --version
```

### 检查CustomTkinter
```bash
python -c "import customtkinter; print('CustomTkinter已安装')"
```

## 🛠️ 手动安装步骤

如果自动安装失败，请按以下步骤手动安装：

### 1. 安装CustomTkinter
```bash
pip install customtkinter>=5.2.0
```

### 2. 安装其他依赖
```bash
pip install -r requirements.txt
```

### 3. 直接启动
```bash
python run_modern_gui.py
```

## 🔍 调试信息

如果仍有问题，请运行以下命令收集调试信息：

```bash
# 检查Python路径
where python

# 检查已安装的包
pip list | findstr customtkinter

# 检查项目文件
dir run_modern_gui.py

# 测试导入
python -c "import sys; print('Python路径:', sys.executable)"
python -c "import customtkinter; print('CustomTkinter版本:', customtkinter.__version__)"
```

## 📞 获取帮助

如果以上方法都无法解决问题，请提供以下信息：

1. 操作系统版本
2. Python版本 (`python --version`)
3. 错误信息截图
4. 使用的启动方法
5. 调试信息输出

## ✅ 成功启动标志

当看到以下信息时，说明启动成功：
```
🎨 Pixiv Spider - 现代化GUI启动器
==================================================
🔄 正在导入模块...
✅ CustomTkinter 已安装
✅ 模块导入成功
🔧 正在初始化配置...
🚀 正在启动现代化GUI界面...
🎉 现代化GUI启动成功！
```

然后会弹出现代化的GUI窗口。
